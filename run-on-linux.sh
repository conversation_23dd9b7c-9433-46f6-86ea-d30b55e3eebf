#!/bin/bash
# Simple script to run VPSScriptHelper-BlueBlue on Linux

# Default values
USERNAME=""
REPOSITORY="vpsscripthelper-blueblue"
TAG="latest"
CONTAINER_NAME="vpsscripthelper"
PORT="8000"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--username)
            USERNAME="$2"
            shift 2
            ;;
        -r|--repository)
            REPOSITORY="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -n|--name)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -u, --username     Docker Hub username (required)"
            echo "  -r, --repository   Repository name (default: vpsscripthelper-blueblue)"
            echo "  -t, --tag          Image tag (default: latest)"
            echo "  -p, --port         Host port (default: 8000)"
            echo "  -n, --name         Container name (default: vpsscripthelper)"
            echo "  -h, --help         Show this help"
            echo ""
            echo "Examples:"
            echo "  $0 -u myusername"
            echo "  $0 -u myusername -t v1.0.0 -p 8080"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Get username if not provided
if [ -z "$USERNAME" ]; then
    read -p "Enter Docker Hub username: " USERNAME
fi

if [ -z "$USERNAME" ]; then
    echo "Error: Username is required"
    exit 1
fi

IMAGE_NAME="$USERNAME/$REPOSITORY:$TAG"

echo "=== VPSScriptHelper-BlueBlue Linux Deployment ==="
echo "Image: $IMAGE_NAME"
echo "Container: $CONTAINER_NAME"
echo "Port: $PORT"
echo ""

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Error: Docker is not installed"
    echo "Install Docker: https://docs.docker.com/engine/install/"
    exit 1
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    echo "Error: Docker daemon is not running"
    echo "Start Docker: sudo systemctl start docker"
    exit 1
fi

echo "✓ Docker is running"

# Stop and remove existing container if it exists
if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    echo "Stopping existing container..."
    docker stop $CONTAINER_NAME
    docker rm $CONTAINER_NAME
fi

# Pull latest image
echo "Pulling image from Docker Hub..."
if ! docker pull $IMAGE_NAME; then
    echo "Error: Failed to pull image $IMAGE_NAME"
    echo "Make sure the image exists on Docker Hub"
    exit 1
fi

echo "✓ Image pulled successfully"

# Create data directories
echo "Creating data directories..."
mkdir -p ./data/uploads ./data/backups ./logs

# Run container
echo "Starting container..."
docker run -d \
    --name $CONTAINER_NAME \
    -p $PORT:8000 \
    -v $(pwd)/data:/app/data \
    -v $(pwd)/logs:/app/logs \
    -e SECRET_KEY="$(openssl rand -base64 32)" \
    -e ENVIRONMENT=production \
    --restart unless-stopped \
    $IMAGE_NAME

if [ $? -eq 0 ]; then
    echo ""
    echo "=== SUCCESS ==="
    echo "Container is running!"
    echo "Application URL: http://localhost:$PORT"
    echo "API Documentation: http://localhost:$PORT/docs"
    echo "Health Check: http://localhost:$PORT/api/v1/health"
    echo ""
    echo "Container logs: docker logs $CONTAINER_NAME"
    echo "Stop container: docker stop $CONTAINER_NAME"
    echo "Remove container: docker rm $CONTAINER_NAME"
else
    echo "Error: Failed to start container"
    exit 1
fi
