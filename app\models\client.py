"""
Client model for VPN clients
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, <PERSON>, <PERSON>olean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from app.core.database import Base
from app.utils.date_utils import is_date_expired, days_until_expiry


class Client(Base):
    """Client model representing a VPN client configuration."""
    
    __tablename__ = "clients"
    
    id = Column(Integer, primary_key=True, index=True)
    server_id = Column(Integer, ForeignKey("servers.id"), nullable=False)
    
    # Client identification
    client_id = Column(String(255), unique=True, index=True, nullable=False)  # UUID
    email = Column(String(255), index=True, nullable=False)
    shopee_username = Column(String(255), index=True, nullable=True)
    
    # Client configuration
    expired_date = Column(String(20), nullable=False)  # Format: DD-MM-YYYY
    is_active = Column(Boolean, default=True)
    
    # Additional metadata
    description = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # Usage tracking
    last_connected = Column(DateTime, nullable=True)
    data_usage = Column(Integer, default=0)  # In bytes
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    server = relationship("Server", back_populates="clients")
    
    def __repr__(self):
        return f"<Client(id={self.id}, email='{self.email}', server_id={self.server_id})>"
    
    @property
    def is_expired(self) -> bool:
        """Check if client is expired based on expired_date."""
        return is_date_expired(self.expired_date)
    
    @property
    def days_until_expiry(self) -> int:
        """Get number of days until expiry (negative if expired)."""
        return days_until_expiry(self.expired_date)
    
    def to_dict(self):
        """Convert client to dictionary."""
        return {
            "id": self.id,
            "server_id": self.server_id,
            "client_id": self.client_id,
            "email": self.email,
            "shopee_username": self.shopee_username,
            "expired_date": self.expired_date,
            "is_active": self.is_active,
            "is_expired": self.is_expired,
            "days_until_expiry": self.days_until_expiry,
            "description": self.description,
            "notes": self.notes,
            "last_connected": self.last_connected.isoformat() if self.last_connected else None,
            "data_usage": self.data_usage,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def to_xray_config(self):
        """Convert client to Xray configuration format."""
        return {
            "id": self.client_id,
            "shopeeUsername": self.shopee_username or self.email,
            "expired_date": self.expired_date,
            "email": self.email
        }
