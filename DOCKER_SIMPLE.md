# 简单Docker部署指南

## 🚀 快速部署到Docker Hub

### Windows开发环境推送到Docker Hub

#### 方法1: PowerShell脚本 (推荐)
```powershell
# 简单推送 (会自动重新构建镜像)
.\simple-push.ps1

# 指定用户名和标签
.\simple-push.ps1 -Username "yourusername" -Tag "v1.0.0"
```

> **注意**: 脚本会使用 `--no-cache` 重新构建镜像以确保包含所有依赖

#### 方法2: 批处理脚本
```cmd
# 简单推送 (会自动重新构建镜像)
push-to-dockerhub.bat
```

#### 方法3: 手动命令
```bash
# 登录Docker Hub
docker login

# 构建镜像 (Linux兼容，强制重新构建)
docker build --no-cache --platform linux/amd64 -t yourusername/vpsscripthelper-blueblue:latest .

# 推送到Docker Hub
docker push yourusername/vpsscripthelper-blueblue:latest
```

## 🐧 Linux服务器部署

### 方法1: 使用脚本 (推荐)
```bash
# 下载脚本
wget https://raw.githubusercontent.com/yourusername/VPSScriptHelper-BlueBlue/main/run-on-linux.sh
chmod +x run-on-linux.sh

# 运行
./run-on-linux.sh -u yourusername
```

### 方法2: 手动命令
```bash
# 拉取镜像
docker pull yourusername/vpsscripthelper-blueblue:latest

# 创建数据目录
mkdir -p ./data/uploads ./data/backups ./logs

# 运行容器
docker run -d \
    --name vpsscripthelper \
    -p 8000:8000 \
    -v $(pwd)/data:/app/data \
    -v $(pwd)/logs:/app/logs \
    -e SECRET_KEY="your-secret-key-here" \
    --restart unless-stopped \
    yourusername/vpsscripthelper-blueblue:latest
```

## 📋 使用说明

### 1. Windows推送镜像
1. 确保Docker Desktop运行
2. 运行 `simple-push.ps1` 脚本
3. 输入Docker Hub用户名
4. 等待构建和推送完成

### 2. Linux部署应用
1. 确保Docker已安装
2. 运行 `run-on-linux.sh` 脚本
3. 输入Docker Hub用户名
4. 访问 `http://your-server:8000`

## 🔧 环境变量

### 必需的环境变量
```bash
SECRET_KEY=your-very-secure-secret-key
```

### 可选的环境变量
```bash
ENVIRONMENT=production
DEBUG=false
PORT=8000
DATABASE_URL=sqlite+aiosqlite:///./data/blueblue.db
ALLOWED_HOSTS=yourdomain.com,localhost
LOG_LEVEL=WARNING
```

## 📁 数据持久化

容器会自动创建以下目录：
- `./data/uploads` - 上传文件
- `./data/backups` - 备份文件
- `./logs` - 应用日志
- `./data/blueblue.db` - SQLite数据库

## 🔍 常用命令

```bash
# 查看容器状态
docker ps

# 查看日志
docker logs vpsscripthelper

# 进入容器
docker exec -it vpsscripthelper /bin/bash

# 停止容器
docker stop vpsscripthelper

# 重启容器
docker restart vpsscripthelper

# 删除容器
docker rm vpsscripthelper

# 更新应用
docker pull yourusername/vpsscripthelper-blueblue:latest
docker stop vpsscripthelper
docker rm vpsscripthelper
# 然后重新运行容器
```

## 🌐 访问应用

- **主页**: http://your-server:8000
- **API文档**: http://your-server:8000/docs
- **健康检查**: http://your-server:8000/api/v1/health

## ⚠️ 注意事项

1. **安全性**: 生产环境必须更改默认的SECRET_KEY
2. **防火墙**: 确保端口8000已开放
3. **SSL**: 生产环境建议使用HTTPS
4. **备份**: 定期备份data目录
5. **更新**: 定期更新Docker镜像

## 🆘 故障排除

### 容器无法启动
```bash
# 查看详细错误
docker logs vpsscripthelper

# 检查端口占用
netstat -tlnp | grep 8000
```

### 无法访问应用
```bash
# 检查容器状态
docker ps

# 检查端口映射
docker port vpsscripthelper

# 测试健康检查
curl http://localhost:8000/api/v1/health
```

### 权限问题
```bash
# 检查文件权限
ls -la data/

# 修复权限
sudo chown -R 1000:1000 data/ logs/
```

## 📞 获取帮助

如果遇到问题：
1. 检查Docker日志: `docker logs vpsscripthelper`
2. 查看应用日志: `cat logs/app.log`
3. 确认环境变量设置正确
4. 检查防火墙和网络设置
