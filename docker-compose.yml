# Docker Compose configuration for VPSScriptHelper-BlueBlue
# Provides both development and production configurations

version: '3.8'

services:
  # Main FastAPI application
  vpsscripthelper:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - ENVIRONMENT=production
    container_name: vpsscripthelper-blueblue
    ports:
      - "8000:8000"
    environment:
      # Application settings
      - ENVIRONMENT=production
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
      
      # Security settings (override in production)
      - SECRET_KEY=${SECRET_KEY:-change-this-in-production}
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      
      # Database settings
      - DATABASE_URL=sqlite+aiosqlite:///./data/blueblue.db
      
      # CORS settings
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-*}
      
      # SSH settings
      - SSH_TIMEOUT=30
      - SSH_RETRY_ATTEMPTS=3
      - SSH_RETRY_DELAY=5
      
      # Xray configuration
      - XRAY_CONFIG_PATH=/etc/xray/config.json
      - XRAY_SERVICE_NAME=xray
      - XRAY_BACKUP_PATH=/etc/xray/config.json.backup
      
      # Task scheduling
      - EXPIRY_CHECK_INTERVAL_HOURS=24
      - AUTO_REMOVE_EXPIRED=true
      
      # Logging
      - LOG_LEVEL=${LOG_LEVEL:-WARNING}
      
      # File paths
      - UPLOAD_DIR=/app/data/uploads
      - BACKUP_DIR=/app/data/backups
    
    volumes:
      # Persistent data storage
      - vpsscripthelper_data:/app/data
      - vpsscripthelper_logs:/app/logs
      
      # Optional: Mount SSH keys for server connections
      # - ${SSH_KEYS_PATH:-./ssh_keys}:/app/ssh_keys:ro
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits (adjust based on your needs)
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Optional: Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: vpsscripthelper-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - vpsscripthelper
    restart: unless-stopped
    profiles:
      - production

  # Optional: PostgreSQL database for production
  postgres:
    image: postgres:15-alpine
    container_name: vpsscripthelper-postgres
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-blueblue}
      - POSTGRES_USER=${POSTGRES_USER:-blueblue}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-change-this-password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    profiles:
      - postgres

volumes:
  # Named volumes for persistent data
  vpsscripthelper_data:
    driver: local
  vpsscripthelper_logs:
    driver: local
  postgres_data:
    driver: local

networks:
  default:
    name: vpsscripthelper-network
