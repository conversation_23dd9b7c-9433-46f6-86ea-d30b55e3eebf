"""
Date and time utility functions
"""

from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
import re

# Special constant for lifetime clients
LIFETIME_VALUE = "lifetime"
LIFETIME_DAYS = 999999  # Large number to represent infinite days


def parse_expiry_date(date_string: str) -> Optional[datetime]:
    """
    Parse expiry date from DD-MM-YYYY format or "lifetime".
    
    Args:
        date_string: Date string in DD-MM-YYYY format or "lifetime"
        
    Returns:
        datetime: Parsed datetime object or None if invalid/lifetime
    """
    if not date_string:
        return None
    
    # Handle lifetime clients
    if date_string.lower().strip() == LIFETIME_VALUE:
        return None  # None indicates no expiry (infinite)
    
    try:
        # Remove any whitespace
        date_string = date_string.strip()
        
        # Validate format with regex
        if not re.match(r'^\d{1,2}-\d{1,2}-\d{4}$', date_string):
            return None
        
        # Parse the date
        day, month, year = map(int, date_string.split('-'))
        
        # Validate ranges
        if not (1 <= day <= 31 and 1 <= month <= 12 and 1900 <= year <= 2100):
            return None
        
        return datetime(year, month, day)
    except (ValueError, AttributeError):
        return None


def format_expiry_date(date_obj: datetime) -> str:
    """
    Format datetime object to DD-MM-YYYY string.
    
    Args:
        date_obj: Datetime object
        
    Returns:
        str: Formatted date string
    """
    return date_obj.strftime("%d-%m-%Y")


def is_date_expired(date_string: str) -> bool:
    """
    Check if a date string represents an expired date.
    
    Args:
        date_string: Date string in DD-MM-YYYY format or "lifetime"
        
    Returns:
        bool: True if expired, False if not expired, lifetime, or invalid date
    """
    # Handle lifetime clients
    if date_string and date_string.lower().strip() == LIFETIME_VALUE:
        return False  # Lifetime clients never expire
    
    expiry_date = parse_expiry_date(date_string)
    if expiry_date is None:
        return True  # Consider invalid dates as expired for safety
    
    return datetime.now() > expiry_date


def days_until_expiry(date_string: str) -> int:
    """
    Calculate days until expiry (negative if already expired).
    
    Args:
        date_string: Date string in DD-MM-YYYY format or "lifetime"
        
    Returns:
        int: Days until expiry (negative if expired, large positive for lifetime, -999 if invalid date)
    """
    # Handle lifetime clients
    if date_string and date_string.lower().strip() == LIFETIME_VALUE:
        return LIFETIME_DAYS  # Large positive number for lifetime
    
    expiry_date = parse_expiry_date(date_string)
    if expiry_date is None:
        return -999  # Invalid date
    
    delta = expiry_date - datetime.now()
    return delta.days


def get_expiry_warning_dates(date_string: str, warning_days: int = 7) -> dict:
    """
    Get expiry warning information.
    
    Args:
        date_string: Date string in DD-MM-YYYY format or "lifetime"
        warning_days: Number of days before expiry to warn
        
    Returns:
        dict: Expiry information including warnings
    """
    # Handle lifetime clients
    if date_string and date_string.lower().strip() == LIFETIME_VALUE:
        return {
            "is_valid": True,
            "is_expired": False,
            "days_until_expiry": LIFETIME_DAYS,
            "needs_warning": False,
            "warning_level": "none",
            "expiry_date": None,
            "formatted_date": LIFETIME_VALUE,
            "is_lifetime": True
        }
    
    expiry_date = parse_expiry_date(date_string)
    if expiry_date is None:
        return {
            "is_valid": False,
            "is_expired": True,
            "days_until_expiry": -999,
            "needs_warning": True,
            "warning_level": "critical",
            "is_lifetime": False
        }
    
    days_left = days_until_expiry(date_string)
    is_expired = days_left < 0
    needs_warning = days_left <= warning_days and not is_expired
    
    # Determine warning level
    warning_level = "none"
    if is_expired:
        warning_level = "critical"
    elif days_left <= 1:
        warning_level = "urgent"
    elif days_left <= 3:
        warning_level = "high"
    elif days_left <= warning_days:
        warning_level = "medium"
    
    return {
        "is_valid": True,
        "is_expired": is_expired,
        "days_until_expiry": days_left,
        "needs_warning": needs_warning or is_expired,
        "warning_level": warning_level,
        "expiry_date": expiry_date.isoformat(),
        "formatted_date": date_string,
        "is_lifetime": False
    }


def validate_date_format(date_string: str) -> bool:
    """
    Validate if date string is in correct DD-MM-YYYY format or "lifetime".
    
    Args:
        date_string: Date string to validate
        
    Returns:
        bool: True if valid format
    """
    if not date_string:
        return False
    
    # Check for lifetime
    if date_string.lower().strip() == LIFETIME_VALUE:
        return True
    
    # Check for DD-MM-YYYY format
    return parse_expiry_date(date_string) is not None


def get_date_range_filter(start_date: str, end_date: str) -> tuple:
    """
    Parse date range for filtering.
    
    Args:
        start_date: Start date string
        end_date: End date string
        
    Returns:
        tuple: (start_datetime, end_datetime) or (None, None) if invalid
    """
    start_dt = parse_expiry_date(start_date) if start_date else None
    end_dt = parse_expiry_date(end_date) if end_date else None
    
    # If end_date is provided, set time to end of day
    if end_dt:
        end_dt = end_dt.replace(hour=23, minute=59, second=59)
    
    return start_dt, end_dt
