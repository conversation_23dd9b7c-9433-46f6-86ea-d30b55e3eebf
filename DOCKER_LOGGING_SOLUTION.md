# Docker Logging Permission Fix

## Problem Summary

You encountered this error when running the application in Docker:

```
Warning: Cannot write to logs directory: [<PERSON>rrno 13] Permission denied: 'logs/test_write.tmp'
Falling back to console-only logging
```

## Root Cause

The issue occurs because:

1. **Volume Mount Permissions**: Docker named volumes can override directory permissions set in the Dockerfile
2. **User Mismatch**: The application runs as `appuser` but the mounted volume may be owned by root
3. **Timing Issue**: Permissions are set before volume mounts, so they get overridden

## Complete Solution

### 1. Enhanced Entrypoint Script (`docker-entrypoint.sh`)

The solution includes a comprehensive entrypoint script that:
- ✅ Automatically detects and fixes permission issues
- ✅ Provides detailed diagnostic information
- ✅ Safely switches from root to appuser
- ✅ Falls back gracefully if permissions can't be fixed

### 2. Updated Dockerfile

Key changes made:
- ✅ Added `gosu` package for secure user switching
- ✅ Container starts as root to allow permission fixes
- ✅ Entrypoint script handles the user switch
- ✅ Fixed case sensitivity warnings

### 3. Development vs Production Configurations

**Production** (`docker-compose.yml`):
- Uses named volumes for persistence
- Automatic permission handling via entrypoint

**Development** (`docker-compose.dev.yml`):
- Uses bind mounts for easier log access
- Override configuration for development needs

## How to Use

### Quick Fix - Rebuild and Run

1. **Rebuild the Docker image**:
   ```bash
   docker-compose build
   ```

2. **Run with automatic permission fixing**:
   ```bash
   # Production
   docker-compose up
   
   # Development
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
   ```

### Testing the Fix

Run the comprehensive test script:

```bash
# Linux/macOS
chmod +x scripts/test-docker-logging.sh
./scripts/test-docker-logging.sh

# Windows
scripts\test-docker-logging.bat
```

### Manual Testing

1. **Check if container starts properly**:
   ```bash
   docker-compose up -d
   docker logs vpsscripthelper-blueblue
   ```

2. **Verify log directory permissions**:
   ```bash
   docker exec vpsscripthelper-blueblue ls -la /app/logs/
   ```

3. **Test write access**:
   ```bash
   docker exec vpsscripthelper-blueblue touch /app/logs/test.log
   docker exec vpsscripthelper-blueblue rm /app/logs/test.log
   ```

## What You Should See

### Successful Startup

```
Starting VPSScriptHelper-BlueBlue container...
Running as user: root:root (UID: 0, GID: 0)
Setting up application directories...
Ensuring directory: /app/logs
  ✅ Created and set permissions for /app/logs (owner: appuser:appuser, permissions: 755)
Testing write permissions...
  ✅ Write permission confirmed for /app/logs
Switching to appuser for application execution...
✅ Logs directory is writable: /app/logs
```

### Application Logs

```
INFO:     Started server process [1]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000
```

## Troubleshooting

### If Issues Persist

1. **Check Docker daemon**: Ensure Docker is running and has internet access
2. **Clear Docker cache**: `docker system prune -a`
3. **Rebuild from scratch**: `docker-compose build --no-cache`
4. **Check volume permissions**: `docker volume inspect vpsscripthelper_logs`

### Fallback Behavior

If file logging still fails, the application will:
- ✅ Continue running normally
- ✅ Log to console only
- ✅ Display helpful diagnostic information
- ✅ Not crash or fail to start

## Files Modified

- ✅ `Dockerfile` - Enhanced with entrypoint and gosu
- ✅ `docker-entrypoint.sh` - New comprehensive permission handler
- ✅ `docker-compose.dev.yml` - Development override configuration
- ✅ `app/utils/logger.py` - Enhanced error reporting and diagnostics
- ✅ `scripts/test-docker-logging.sh` - Automated testing script
- ✅ `scripts/test-docker-logging.bat` - Windows testing script

## Benefits

1. **Automatic Resolution**: No manual intervention needed
2. **Detailed Diagnostics**: Clear information about permission issues
3. **Graceful Degradation**: Application continues working even if logging fails
4. **Security**: Proper user switching and permission handling
5. **Development Friendly**: Easy access to logs in development mode
6. **Production Ready**: Robust handling for production deployments

The solution ensures your application will start successfully and handle logging permissions automatically, regardless of the Docker environment configuration.
