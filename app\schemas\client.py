"""
Client-related Pydantic schemas
"""

from pydantic import BaseModel, field_validator, UUID4, Field
from typing import Optional, List
from datetime import datetime
import re


class ClientBase(BaseModel):
    """Base schema for client information."""
    email: str = Field(..., min_length=1, max_length=255, description="Email or custom identifier")
    shopee_username: Optional[str] = Field(None, max_length=255)
    expired_date: str = Field(..., description="Expiry date in DD-MM-YYYY format or 'lifetime'")
    description: Optional[str] = Field(None, max_length=500)
    notes: Optional[str] = Field(None, max_length=1000)
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        # Allow any non-empty string as email can be a custom identifier
        if not v or not v.strip():
            raise ValueError('Email/identifier cannot be empty')
        return v.strip()
    
    @field_validator('expired_date')
    @classmethod
    def validate_expired_date(cls, v):
        # Check for lifetime
        if v.lower().strip() == 'lifetime':
            return 'lifetime'
        
        # Check for DD-MM-YYYY format
        if not re.match(r'^\d{1,2}-\d{1,2}-\d{4}$', v):
            raise ValueError('Date must be in DD-MM-YYYY format or "lifetime"')
        
        try:
            day, month, year = map(int, v.split('-'))
            if not (1 <= day <= 31 and 1 <= month <= 12 and 1900 <= year <= 2100):
                raise ValueError('Invalid date values')
        except ValueError:
            raise ValueError('Invalid date format')
        
        return v


class ClientCreate(ClientBase):
    """Schema for creating a new client."""
    server_id: int
    client_id: Optional[str] = None  # UUID, will be generated if not provided
    
    @field_validator('client_id')
    @classmethod
    def validate_client_id(cls, v):
        if v is not None:
            try:
                # Validate UUID format
                import uuid
                uuid.UUID(v)
            except ValueError:
                raise ValueError('client_id must be a valid UUID')
        return v


class ClientUpdate(BaseModel):
    """Schema for updating client information."""
    email: Optional[str] = None
    shopee_username: Optional[str] = None
    expired_date: Optional[str] = None
    description: Optional[str] = None
    notes: Optional[str] = None
    is_active: Optional[bool] = None
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        if v is not None:
            # Allow any non-empty string as email can be a custom identifier
            if not v or not v.strip():
                raise ValueError('Email/identifier cannot be empty')
            return v.strip()
        return v
    
    @field_validator('expired_date')
    @classmethod
    def validate_expired_date(cls, v):
        if v is not None:
            # Check for lifetime
            if v.lower().strip() == 'lifetime':
                return 'lifetime'
            
            # Check for DD-MM-YYYY format
            if not re.match(r'^\d{1,2}-\d{1,2}-\d{4}$', v):
                raise ValueError('Date must be in DD-MM-YYYY format or "lifetime"')
            
            try:
                day, month, year = map(int, v.split('-'))
                if not (1 <= day <= 31 and 1 <= month <= 12 and 1900 <= year <= 2100):
                    raise ValueError('Invalid date values')
            except ValueError:
                raise ValueError('Invalid date format')
        
        return v


class ClientResponse(ClientBase):
    """Schema for client response."""
    id: int
    server_id: int
    client_id: str  # UUID
    is_active: bool
    is_expired: bool
    days_until_expiry: int
    last_connected: Optional[datetime] = None
    data_usage: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ClientListResponse(BaseModel):
    """Schema for client list response with pagination."""
    clients: List[ClientResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class ClientXrayConfig(BaseModel):
    """Schema for Xray client configuration."""
    id: str  # UUID
    shopeeUsername: str
    expired_date: str
    email: str


class ClientBulkCreate(BaseModel):
    """Schema for bulk client creation."""
    server_id: int
    clients: List[ClientBase]


class ClientBulkResponse(BaseModel):
    """Schema for bulk operation response."""
    success: bool
    created_count: int
    failed_count: int
    created_clients: List[ClientResponse]
    errors: List[str]


class ClientExpiryInfo(BaseModel):
    """Schema for client expiry information."""
    client_id: str
    email: str
    expired_date: str
    is_expired: bool
    days_until_expiry: int
    warning_level: str  # none, medium, high, urgent, critical


class ClientUsageStats(BaseModel):
    """Schema for client usage statistics."""
    client_id: str
    email: str
    data_usage: int
    last_connected: Optional[datetime] = None
    connection_count: int
    avg_session_duration: Optional[float] = None 