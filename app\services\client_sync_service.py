"""
Client synchronization service for importing clients from remote server configurations
"""

import logging
from typing import Dict, List, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.core.database import AsyncSessionLocal
from app.models.server import Server
from app.models.client import Client
from app.services.config_service import ConfigService
from app.utils.date_utils import validate_date_format

logger = logging.getLogger(__name__)


class ClientSyncService:
    """Service for synchronizing clients between remote servers and local database."""
    
    def __init__(self):
        self.config_service = ConfigService()
    
    async def sync_server_clients(self, server_id: int) -> Dict[str, Any]:
        """
        Sync clients from a specific server's Xray configuration to local database.
        
        Args:
            server_id: Server ID to sync
            
        Returns:
            dict: Sync results
        """
        async with AsyncSessionLocal() as db:
            # Get server
            result = await db.execute(select(Server).where(Server.id == server_id))
            server = result.scalar_one_or_none()
            
            if not server:
                return {
                    "success": False,
                    "error": "Server not found"
                }
            
            try:
                # Get clients from server configuration
                config_clients = self.config_service.get_clients_from_config(server)
                
                if not config_clients:
                    return {
                        "success": True,
                        "server_id": server_id,
                        "server_name": server.name,
                        "total_remote_clients": 0,
                        "imported_count": 0,
                        "updated_count": 0,
                        "skipped_count": 0,
                        "errors": [],
                        "message": "No clients found in server configuration"
                    }
                
                imported_count = 0
                updated_count = 0
                skipped_count = 0
                errors = []
                
                for config_client in config_clients:
                    try:
                        result = await self._sync_single_client(db, server, config_client)
                        
                        if result["action"] == "imported":
                            imported_count += 1
                        elif result["action"] == "updated":
                            updated_count += 1
                        elif result["action"] == "skipped":
                            skipped_count += 1
                            
                    except Exception as e:
                        error_msg = f"Error syncing client {config_client.get('email', 'unknown')}: {str(e)}"
                        errors.append(error_msg)
                        logger.error(error_msg)
                
                await db.commit()
                
                logger.info(f"Client sync completed for server {server.name}: "
                           f"{imported_count} imported, {updated_count} updated, "
                           f"{skipped_count} skipped, {len(errors)} errors")
                
                return {
                    "success": True,
                    "server_id": server_id,
                    "server_name": server.name,
                    "total_remote_clients": len(config_clients),
                    "imported_count": imported_count,
                    "updated_count": updated_count,
                    "skipped_count": skipped_count,
                    "errors": errors,
                    "message": f"Sync completed: {imported_count + updated_count} clients processed"
                }
                
            except Exception as e:
                logger.error(f"Error syncing clients from server {server.name}: {str(e)}")
                return {
                    "success": False,
                    "server_id": server_id,
                    "server_name": server.name,
                    "error": str(e)
                }
    
    async def _sync_single_client(self, db: AsyncSession, server: Server, config_client: Dict[str, Any]) -> Dict[str, str]:
        """
        Sync a single client from config to database.
        
        Args:
            db: Database session
            server: Server instance
            config_client: Client configuration from Xray
            
        Returns:
            dict: Sync result for this client
        """
        client_id = config_client.get("id")
        email = config_client.get("email", "")
        shopee_username = config_client.get("shopeeUsername", email)
        expired_date = config_client.get("expired_date", "")
        
        # Enhanced validation with better error messages
        validation_errors = []
        
        if not client_id:
            validation_errors.append("Client ID is missing")
        
        if not email:
            validation_errors.append("Email is missing")
        
        if not expired_date:
            validation_errors.append("Expired date is missing")
        elif not validate_date_format(expired_date):
            validation_errors.append(f"Invalid date format: {expired_date}")
        
        # If there are validation errors, provide detailed error message
        if validation_errors:
            client_info = f"Client ID: {client_id or 'N/A'}, Email: {email or 'N/A'}"
            error_msg = f"Validation failed for {client_info} - {'; '.join(validation_errors)}"
            raise ValueError(error_msg)
        
        # Check if client already exists in database
        result = await db.execute(
            select(Client).where(
                and_(Client.server_id == server.id, Client.client_id == client_id)
            )
        )
        existing_client = result.scalar_one_or_none()
        
        if existing_client:
            # Update existing client if data has changed
            updated = False
            
            if existing_client.email != email:
                existing_client.email = email
                updated = True
            
            if existing_client.shopee_username != shopee_username:
                existing_client.shopee_username = shopee_username
                updated = True
            
            if existing_client.expired_date != expired_date:
                existing_client.expired_date = expired_date
                updated = True
            
            if updated:
                logger.info(f"Updated client {email} from server config")
                return {"action": "updated", "client_id": client_id}
            else:
                return {"action": "skipped", "client_id": client_id}
        
        else:
            # Check if email already exists with different client_id
            result = await db.execute(
                select(Client).where(
                    and_(Client.server_id == server.id, Client.email == email)
                )
            )
            email_conflict = result.scalar_one_or_none()
            
            if email_conflict:
                raise ValueError(f"Email {email} already exists with different client ID")
            
            # Create new client
            new_client = Client(
                server_id=server.id,
                client_id=client_id,
                email=email,
                shopee_username=shopee_username,
                expired_date=expired_date,
                description=f"Imported from server {server.name}",
                is_active=True
            )
            
            db.add(new_client)
            logger.info(f"Imported new client {email} from server config")
            return {"action": "imported", "client_id": client_id}
    
    async def sync_all_servers(self) -> Dict[str, Any]:
        """
        Sync clients from all active servers.
        
        Returns:
            dict: Overall sync results
        """
        async with AsyncSessionLocal() as db:
            # Get all active servers
            result = await db.execute(select(Server).where(Server.is_active == True))
            servers = result.scalars().all()
            
            if not servers:
                return {
                    "success": True,
                    "total_servers": 0,
                    "message": "No active servers found"
                }
            
            total_imported = 0
            total_updated = 0
            total_errors = 0
            server_results = []
            
            for server in servers:
                try:
                    result = await self.sync_server_clients(server.id)
                    server_results.append(result)
                    
                    if result.get("success"):
                        total_imported += result.get("imported_count", 0)
                        total_updated += result.get("updated_count", 0)
                        total_errors += len(result.get("errors", []))
                    else:
                        total_errors += 1
                        
                except Exception as e:
                    error_result = {
                        "success": False,
                        "server_id": server.id,
                        "server_name": server.name,
                        "error": str(e)
                    }
                    server_results.append(error_result)
                    total_errors += 1
                    logger.error(f"Error syncing server {server.name}: {str(e)}")
            
            return {
                "success": True,
                "total_servers": len(servers),
                "total_imported": total_imported,
                "total_updated": total_updated,
                "total_errors": total_errors,
                "server_results": server_results,
                "message": f"Synced {len(servers)} servers: {total_imported} imported, {total_updated} updated"
            }
    
    async def compare_server_clients(self, server_id: int) -> Dict[str, Any]:
        """
        Compare clients between server configuration and local database.
        
        Args:
            server_id: Server ID to compare
            
        Returns:
            dict: Comparison results
        """
        async with AsyncSessionLocal() as db:
            # Get server
            result = await db.execute(select(Server).where(Server.id == server_id))
            server = result.scalar_one_or_none()
            
            if not server:
                return {
                    "success": False,
                    "error": "Server not found"
                }
            
            try:
                # Get clients from server configuration
                config_clients = self.config_service.get_clients_from_config(server)
                config_client_ids = {c.get("id") for c in config_clients if c.get("id")}
                
                # Get clients from database
                result = await db.execute(select(Client).where(Client.server_id == server_id))
                db_clients = result.scalars().all()
                db_client_ids = {c.client_id for c in db_clients}
                
                # Find differences
                only_in_config = config_client_ids - db_client_ids
                only_in_database = db_client_ids - config_client_ids
                in_both = config_client_ids & db_client_ids
                
                # Get detailed info for clients only in config
                missing_in_db = []
                for config_client in config_clients:
                    if config_client.get("id") in only_in_config:
                        missing_in_db.append({
                            "client_id": config_client.get("id"),
                            "email": config_client.get("email"),
                            "expired_date": config_client.get("expired_date")
                        })
                
                # Get detailed info for clients only in database
                missing_in_config = []
                for db_client in db_clients:
                    if db_client.client_id in only_in_database:
                        missing_in_config.append({
                            "client_id": db_client.client_id,
                            "email": db_client.email,
                            "expired_date": db_client.expired_date
                        })
                
                return {
                    "success": True,
                    "server_id": server_id,
                    "server_name": server.name,
                    "total_in_config": len(config_client_ids),
                    "total_in_database": len(db_client_ids),
                    "in_sync": len(in_both),
                    "missing_in_database": len(only_in_config),
                    "missing_in_config": len(only_in_database),
                    "missing_in_db_details": missing_in_db,
                    "missing_in_config_details": missing_in_config,
                    "is_synchronized": len(only_in_config) == 0 and len(only_in_database) == 0
                }
                
            except Exception as e:
                logger.error(f"Error comparing clients for server {server.name}: {str(e)}")
                return {
                    "success": False,
                    "server_id": server_id,
                    "server_name": server.name,
                    "error": str(e)
                }
    
    async def analyze_invalid_clients(self, server_id: int) -> Dict[str, Any]:
        """
        Analyze invalid clients in server configuration.
        
        Args:
            server_id: Server ID to analyze
            
        Returns:
            dict: Analysis results with invalid clients details
        """
        async with AsyncSessionLocal() as db:
            # Get server
            result = await db.execute(select(Server).where(Server.id == server_id))
            server = result.scalar_one_or_none()
            
            if not server:
                return {
                    "success": False,
                    "error": "Server not found"
                }
            
            try:
                # Get clients from server configuration
                config_clients = self.config_service.get_clients_from_config(server)
                
                valid_clients = []
                invalid_clients = []
                
                for i, config_client in enumerate(config_clients):
                    client_id = config_client.get("id")
                    email = config_client.get("email", "")
                    expired_date = config_client.get("expired_date", "")
                    shopee_username = config_client.get("shopeeUsername", "")
                    
                    issues = []
                    if not client_id:
                        issues.append("missing_client_id")
                    if not email:
                        issues.append("missing_email")
                    if not expired_date:
                        issues.append("missing_expired_date")
                    elif not validate_date_format(expired_date):
                        issues.append("invalid_date_format")
                    
                    if issues:
                        invalid_clients.append({
                            "index": i,
                            "client_id": client_id,
                            "email": email,
                            "shopee_username": shopee_username,
                            "expired_date": expired_date,
                            "issues": issues,
                            "raw_data": config_client
                        })
                    else:
                        valid_clients.append(config_client)
                
                return {
                    "success": True,
                    "server_id": server_id,
                    "server_name": server.name,
                    "total_clients": len(config_clients),
                    "valid_clients": len(valid_clients),
                    "invalid_clients": len(invalid_clients),
                    "invalid_client_details": invalid_clients,
                    "analysis": {
                        "missing_client_id": len([c for c in invalid_clients if "missing_client_id" in c["issues"]]),
                        "missing_email": len([c for c in invalid_clients if "missing_email" in c["issues"]]),
                        "missing_expired_date": len([c for c in invalid_clients if "missing_expired_date" in c["issues"]]),
                        "invalid_date_format": len([c for c in invalid_clients if "invalid_date_format" in c["issues"]])
                    }
                }
                
            except Exception as e:
                logger.error(f"Error analyzing invalid clients for server {server.name}: {str(e)}")
                return {
                    "success": False,
                    "server_id": server_id,
                    "server_name": server.name,
                    "error": str(e)
                }

    async def cleanup_orphaned_clients(self, server_id: int) -> Dict[str, Any]:
        """
        Remove clients from database that don't exist in server configuration.
        
        Args:
            server_id: Server ID to clean up
            
        Returns:
            dict: Cleanup results
        """
        async with AsyncSessionLocal() as db:
            # Get server
            result = await db.execute(select(Server).where(Server.id == server_id))
            server = result.scalar_one_or_none()
            
            if not server:
                return {
                    "success": False,
                    "error": "Server not found"
                }
            
            try:
                # Get comparison data
                comparison = await self.compare_server_clients(server_id)
                
                if not comparison.get("success"):
                    return comparison
                
                # Get clients that exist only in database
                orphaned_clients = comparison.get("missing_in_config_details", [])
                
                if not orphaned_clients:
                    return {
                        "success": True,
                        "server_id": server_id,
                        "server_name": server.name,
                        "removed_count": 0,
                        "message": "No orphaned clients found"
                    }
                
                # Remove orphaned clients
                removed_count = 0
                for orphaned in orphaned_clients:
                    result = await db.execute(
                        select(Client).where(
                            and_(
                                Client.server_id == server_id,
                                Client.client_id == orphaned["client_id"]
                            )
                        )
                    )
                    client = result.scalar_one_or_none()
                    
                    if client:
                        await db.delete(client)
                        removed_count += 1
                        logger.info(f"Removed orphaned client {client.email} from database")
                
                await db.commit()
                
                return {
                    "success": True,
                    "server_id": server_id,
                    "server_name": server.name,
                    "removed_count": removed_count,
                    "removed_clients": orphaned_clients,
                    "message": f"Removed {removed_count} orphaned clients from database"
                }
                
            except Exception as e:
                logger.error(f"Error cleaning up orphaned clients for server {server.name}: {str(e)}")
                return {
                    "success": False,
                    "server_id": server_id,
                    "server_name": server.name,
                    "error": str(e)
                } 