# VPSScriptHelper-<PERSON><PERSON><PERSON> Docker ignore file
# Excludes unnecessary files from Docker build context

# Git and version control
.git
.gitignore
.gitattributes

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Testing and coverage
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
htmlcov/

# Documentation
docs/
*.md
!README.md

# Logs and databases (runtime data)
logs/
*.log
*.db
*.sqlite
*.sqlite3

# Uploads and backups (runtime data)
uploads/
backups/
temp/

# SSH keys and sensitive files
*.pem
*.key
id_rsa*
id_ed25519*
known_hosts

# Configuration backups
config.json.backup*

# Docker files (except Dockerfile)
docker-compose*.yml
.dockerignore

# Deployment scripts
deploy-to-dockerhub.ps1
deploy-to-dockerhub.bat
deployment-*.log

# Development and testing files
tests/
test_*.py
*_test.py

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Node.js (if any frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment specific
.env.local
.env.development
.env.test
.env.production

# Alembic (database migration) - keep the alembic directory but exclude versions if needed
# alembic/versions/

# Scripts that shouldn't be in container
scripts/local_*
scripts/dev_*
