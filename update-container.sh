#!/bin/bash
# Force update VPSScriptHelper-BlueBlue container

# Default values
USERNAME=""
REPOSITORY="vpsscripthelper-blueblue"
TAG="latest"
CONTAINER_NAME="vpsscripthelper"
PORT="8000"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--username)
            USERNAME="$2"
            shift 2
            ;;
        -r|--repository)
            REPOSITORY="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -n|--name)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -u, --username     Docker Hub username (required)"
            echo "  -r, --repository   Repository name (default: vpsscripthelper-blueblue)"
            echo "  -t, --tag          Image tag (default: latest)"
            echo "  -p, --port         Host port (default: 8000)"
            echo "  -n, --name         Container name (default: vpsscripthelper)"
            echo "  -h, --help         Show this help"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Get username if not provided
if [ -z "$USERNAME" ]; then
    read -p "Enter Docker Hub username: " USERNAME
fi

if [ -z "$USERNAME" ]; then
    echo "Error: Username is required"
    exit 1
fi

IMAGE_NAME="$USERNAME/$REPOSITORY:$TAG"

echo "=== FORCE UPDATE CONTAINER ==="
echo "Image: $IMAGE_NAME"
echo "Container: $CONTAINER_NAME"
echo "Port: $PORT"
echo ""

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Error: Docker is not installed"
    exit 1
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    echo "Error: Docker daemon is not running"
    exit 1
fi

echo "✓ Docker is running"

# Stop existing container if it exists
if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
    echo "Stopping existing container..."
    docker stop $CONTAINER_NAME
fi

# Remove existing container if it exists
if docker ps -aq -f name=$CONTAINER_NAME | grep -q .; then
    echo "Removing existing container..."
    docker rm $CONTAINER_NAME
fi

# Remove existing image to force fresh pull
echo "Removing existing image..."
docker rmi $IMAGE_NAME 2>/dev/null || echo "No existing image to remove"

# Pull latest image
echo "Pulling latest image from Docker Hub..."
if ! docker pull $IMAGE_NAME; then
    echo "Error: Failed to pull image $IMAGE_NAME"
    exit 1
fi

echo "✓ Image pulled successfully"

# Verify email-validator is in the new image
echo "Verifying email-validator is installed..."
if docker run --rm $IMAGE_NAME pip list | grep -q email-validator; then
    echo "✓ email-validator is installed in the image"
else
    echo "✗ email-validator NOT found in the image"
    echo "The image may not have been rebuilt properly"
    exit 1
fi

# Create data directories
echo "Creating data directories..."
mkdir -p ./data/uploads ./data/backups ./logs

# Run container
echo "Starting new container..."
docker run -d \
    --name $CONTAINER_NAME \
    -p $PORT:8000 \
    -v $(pwd)/data:/app/data \
    -v $(pwd)/logs:/app/logs \
    -e SECRET_KEY="$(openssl rand -base64 32)" \
    -e ENVIRONMENT=production \
    --restart unless-stopped \
    $IMAGE_NAME

if [ $? -eq 0 ]; then
    echo ""
    echo "=== SUCCESS ==="
    echo "Container is running with updated image!"
    echo "Application URL: http://localhost:$PORT"
    echo "API Documentation: http://localhost:$PORT/docs"
    echo "Health Check: http://localhost:$PORT/api/v1/health"
    echo ""
    echo "Check logs: docker logs $CONTAINER_NAME"
    echo "Check if email-validator error is gone!"
else
    echo "Error: Failed to start container"
    exit 1
fi
