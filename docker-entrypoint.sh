#!/bin/bash
set -e

# Docker entrypoint script for VPSScriptHelper-BlueBlue
# Ensures proper permissions and directory setup before starting the application

echo "Starting VPSScriptHelper-BlueBlue container..."

# Function to check if running as root
is_root() {
    [ "$(id -u)" = "0" ]
}

# Function to ensure directory exists and has proper permissions
ensure_directory() {
    local dir="$1"
    local owner="$2"
    local permissions="$3"
    
    echo "Ensuring directory: $dir"
    
    if is_root; then
        # Running as root - create directory and set ownership
        mkdir -p "$dir"
        chown -R "$owner" "$dir"
        chmod -R "$permissions" "$dir"
        echo "  ✅ Created and set permissions for $dir (owner: $owner, permissions: $permissions)"
    else
        # Running as non-root user - create directory if possible
        if [ -w "$(dirname "$dir")" ]; then
            mkdir -p "$dir"
            chmod "$permissions" "$dir" 2>/dev/null || echo "  ⚠️  Could not set permissions for $dir (not root)"
            echo "  ✅ Created directory $dir"
        else
            echo "  ⚠️  Cannot create $dir (no write permission to parent directory)"
        fi
    fi
}

# Function to test write permissions
test_write_permission() {
    local dir="$1"
    local test_file="$dir/test_write.tmp"
    
    if [ -d "$dir" ]; then
        if touch "$test_file" 2>/dev/null; then
            rm -f "$test_file"
            echo "  ✅ Write permission confirmed for $dir"
            return 0
        else
            echo "  ❌ No write permission for $dir"
            return 1
        fi
    else
        echo "  ❌ Directory $dir does not exist"
        return 1
    fi
}

# Ensure required directories exist with proper permissions
echo "Setting up application directories..."

# Get current user info
CURRENT_USER=$(id -un)
CURRENT_GROUP=$(id -gn)
echo "Running as user: $CURRENT_USER:$CURRENT_GROUP (UID: $(id -u), GID: $(id -g))"

# Define directories and their required permissions
DIRECTORIES=(
    "/app/logs:$CURRENT_USER:$CURRENT_GROUP:755"
    "/app/data:$CURRENT_USER:$CURRENT_GROUP:755"
    "/app/uploads:$CURRENT_USER:$CURRENT_GROUP:755"
    "/app/backups:$CURRENT_USER:$CURRENT_GROUP:755"
    "/app/config:$CURRENT_USER:$CURRENT_GROUP:755"
)

# Create directories
for dir_spec in "${DIRECTORIES[@]}"; do
    IFS=':' read -r dir owner group permissions <<< "$dir_spec"
    ensure_directory "$dir" "$owner:$group" "$permissions"
done

# Test write permissions for critical directories
echo "Testing write permissions..."
test_write_permission "/app/logs"
LOGS_WRITABLE=$?

test_write_permission "/app/data"
DATA_WRITABLE=$?

# If running as root, switch to appuser for the main application
if is_root; then
    echo "Switching to appuser for application execution..."
    
    # Ensure appuser owns the application files
    chown -R appuser:appuser /app
    
    # Execute the command as appuser
    exec gosu appuser "$@"
else
    echo "Running as non-root user: $CURRENT_USER"
    
    # Check if we have necessary permissions
    if [ $LOGS_WRITABLE -ne 0 ]; then
        echo "⚠️  Warning: Cannot write to logs directory. File logging will be disabled."
    fi
    
    if [ $DATA_WRITABLE -ne 0 ]; then
        echo "⚠️  Warning: Cannot write to data directory. This may cause application issues."
    fi
    
    # Execute the command directly
    exec "$@"
fi
