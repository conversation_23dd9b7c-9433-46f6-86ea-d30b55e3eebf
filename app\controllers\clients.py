"""
Client management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from typing import List, Optional
import logging
import uuid

from app.core.database import get_db
from app.core.security import generate_client_id
from app.dependencies import get_current_user
from app.models.user import User
from app.models.client import Client
from app.models.server import Server
from app.schemas.client import (
    ClientCreate, ClientUpdate, ClientResponse, ClientListResponse,
    ClientBulkCreate, ClientBulkResponse, ClientExpiryInfo
)
from app.services.config_service import ConfigService
from app.services.client_sync_service import ClientSyncService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=ClientListResponse)
async def get_clients(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    server_id: Optional[int] = Query(None),
    is_active: Optional[bool] = Query(None),
    is_expired: Optional[bool] = Query(None),
    search: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of clients with filtering and pagination."""
    query = select(Client)
    
    # Apply filters
    filters = []
    if server_id is not None:
        filters.append(Client.server_id == server_id)
    if is_active is not None:
        filters.append(Client.is_active == is_active)
    if search:
        search_filter = or_(
            Client.email.ilike(f"%{search}%"),
            Client.shopee_username.ilike(f"%{search}%"),
            Client.client_id.ilike(f"%{search}%")
        )
        filters.append(search_filter)
    
    if filters:
        query = query.where(and_(*filters))
    
    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # Apply pagination and get results
    query = query.offset(skip).limit(limit).order_by(Client.created_at.desc())
    result = await db.execute(query)
    clients = result.scalars().all()
    
    # Filter by expiry status if requested
    if is_expired is not None:
        clients = [c for c in clients if c.is_expired == is_expired]
    
    # Calculate pagination info
    page = (skip // limit) + 1
    has_next = skip + limit < total
    has_prev = skip > 0
    
    return ClientListResponse(
        clients=[ClientResponse.model_validate(client) for client in clients],
        total=total,
        page=page,
        per_page=limit,
        has_next=has_next,
        has_prev=has_prev
    )


@router.post("/", response_model=ClientResponse)
async def create_client(
    client_data: ClientCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new client and add to server configuration."""
    # Verify server exists
    result = await db.execute(select(Server).where(Server.id == client_data.server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    # Generate client ID if not provided
    client_id = client_data.client_id or generate_client_id()
    
    # Check if client ID already exists
    result = await db.execute(select(Client).where(Client.client_id == client_id))
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Client ID already exists"
        )
    
    # Check if email already exists on this server
    result = await db.execute(
        select(Client).where(
            and_(Client.server_id == client_data.server_id, Client.email == client_data.email)
        )
    )
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Client with this email already exists on this server"
        )
    
    # Create client in database
    client = Client(
        server_id=client_data.server_id,
        client_id=client_id,
        email=client_data.email,
        shopee_username=client_data.shopee_username or client_data.email,
        expired_date=client_data.expired_date,
        description=client_data.description,
        notes=client_data.notes
    )
    
    db.add(client)
    await db.commit()
    await db.refresh(client)
    
    # Add client to server configuration
    try:
        config_service = ConfigService()
        success = config_service.add_client_to_config(server, client)
        
        if not success:
            # Rollback database changes if config update fails
            await db.delete(client)
            await db.commit()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to add client to server configuration"
            )
        
        logger.info(f"Client created: {client.email} on server {server.name} by user {current_user.username}")
        return ClientResponse.model_validate(client)
        
    except Exception as e:
        # Rollback database changes if config update fails
        await db.delete(client)
        await db.commit()
        logger.error(f"Error adding client to config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add client to server configuration: {str(e)}"
        )


@router.get("/{client_id}", response_model=ClientResponse)
async def get_client(
    client_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get client by ID."""
    result = await db.execute(select(Client).where(Client.id == client_id))
    client = result.scalar_one_or_none()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    return ClientResponse.model_validate(client)


@router.put("/{client_id}", response_model=ClientResponse)
async def update_client(
    client_id: int,
    client_data: ClientUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update client information."""
    result = await db.execute(select(Client).where(Client.id == client_id))
    client = result.scalar_one_or_none()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Get server for config updates
    result = await db.execute(select(Server).where(Server.id == client.server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Associated server not found"
        )
    
    # Check if new email conflicts with existing client on same server
    if client_data.email and client_data.email != client.email:
        result = await db.execute(
            select(Client).where(
                and_(
                    Client.server_id == client.server_id,
                    Client.email == client_data.email,
                    Client.id != client_id
                )
            )
        )
        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Client with this email already exists on this server"
            )
    
    # Update client fields
    update_data = client_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(client, field, value)
    
    await db.commit()
    await db.refresh(client)
    
    # Update server configuration
    try:
        config_service = ConfigService()
        success = config_service.add_client_to_config(server, client)  # This will update existing client
        
        if not success:
            logger.warning(f"Failed to update client in server configuration: {client.email}")
        
        logger.info(f"Client updated: {client.email} by user {current_user.username}")
        return ClientResponse.model_validate(client)
        
    except Exception as e:
        logger.error(f"Error updating client in config: {str(e)}")
        # Don't fail the request if config update fails, just log it
        return ClientResponse.model_validate(client)


@router.delete("/{client_id}")
async def delete_client(
    client_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete client from database and server configuration."""
    result = await db.execute(select(Client).where(Client.id == client_id))
    client = result.scalar_one_or_none()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Get server for config updates
    result = await db.execute(select(Server).where(Server.id == client.server_id))
    server = result.scalar_one_or_none()
    
    # Remove from server configuration first
    if server:
        try:
            config_service = ConfigService()
            config_service.remove_client_from_config(server, client.client_id)
        except Exception as e:
            logger.error(f"Error removing client from config: {str(e)}")
            # Continue with database deletion even if config update fails
    
    # Delete from database
    await db.delete(client)
    await db.commit()
    
    logger.info(f"Client deleted: {client.email} by user {current_user.username}")
    return {"message": f"Client {client.email} deleted successfully"}


@router.post("/bulk", response_model=ClientBulkResponse)
async def create_clients_bulk(
    bulk_data: ClientBulkCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create multiple clients in bulk."""
    # Verify server exists
    result = await db.execute(select(Server).where(Server.id == bulk_data.server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    created_clients = []
    errors = []
    
    for client_data in bulk_data.clients:
        try:
            # Generate client ID
            client_id = generate_client_id()
            
            # Check if email already exists on this server
            result = await db.execute(
                select(Client).where(
                    and_(Client.server_id == bulk_data.server_id, Client.email == client_data.email)
                )
            )
            if result.scalar_one_or_none():
                errors.append(f"Client with email {client_data.email} already exists on this server")
                continue
            
            # Create client
            client = Client(
                server_id=bulk_data.server_id,
                client_id=client_id,
                email=client_data.email,
                shopee_username=client_data.shopee_username or client_data.email,
                expired_date=client_data.expired_date,
                description=client_data.description,
                notes=client_data.notes
            )
            
            db.add(client)
            await db.commit()
            await db.refresh(client)
            
            # Add to server configuration
            config_service = ConfigService()
            success = config_service.add_client_to_config(server, client)
            
            if success:
                created_clients.append(ClientResponse.model_validate(client))
            else:
                errors.append(f"Failed to add {client_data.email} to server configuration")
                await db.delete(client)
                await db.commit()
                
        except Exception as e:
            errors.append(f"Error creating client {client_data.email}: {str(e)}")
    
    logger.info(f"Bulk client creation: {len(created_clients)} created, {len(errors)} failed by user {current_user.username}")
    
    return ClientBulkResponse(
        success=len(created_clients) > 0,
        created_count=len(created_clients),
        failed_count=len(errors),
        created_clients=created_clients,
        errors=errors
    )


@router.get("/server/{server_id}/expiry", response_model=List[ClientExpiryInfo])
async def get_server_client_expiry(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get expiry information for all clients on a server."""
    # Verify server exists
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    # Get clients from database
    result = await db.execute(select(Client).where(Client.server_id == server_id))
    clients = result.scalars().all()
    
    expiry_info = []
    for client in clients:
        from app.utils.date_utils import get_expiry_warning_dates
        warning_info = get_expiry_warning_dates(client.expired_date)
        
        expiry_info.append(ClientExpiryInfo(
            client_id=client.client_id,
            email=client.email,
            expired_date=client.expired_date,
            is_expired=client.is_expired,
            days_until_expiry=client.days_until_expiry,
            warning_level=warning_info.get("warning_level", "none")
        ))
    
    return expiry_info


@router.post("/{client_id}/extend")
async def extend_client_expiry(
    client_id: int,
    days: int = Query(..., ge=1, le=365),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Extend client expiry by specified number of days."""
    result = await db.execute(select(Client).where(Client.id == client_id))
    client = result.scalar_one_or_none()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Parse current expiry date and extend it
    from app.utils.date_utils import parse_expiry_date, format_expiry_date
    from datetime import timedelta
    
    current_expiry = parse_expiry_date(client.expired_date)
    if not current_expiry:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid current expiry date format"
        )
    
    new_expiry = current_expiry + timedelta(days=days)
    new_expiry_str = format_expiry_date(new_expiry)
    
    # Update client
    client.expired_date = new_expiry_str
    await db.commit()
    await db.refresh(client)
    
    # Update server configuration
    result = await db.execute(select(Server).where(Server.id == client.server_id))
    server = result.scalar_one_or_none()
    
    if server:
        try:
            config_service = ConfigService()
            config_service.add_client_to_config(server, client)
        except Exception as e:
            logger.error(f"Error updating client expiry in config: {str(e)}")
    
    logger.info(f"Client expiry extended: {client.email} by {days} days by user {current_user.username}")
    
    return {
        "message": f"Client expiry extended by {days} days",
        "old_expiry": current_expiry.strftime("%d-%m-%Y"),
        "new_expiry": new_expiry_str,
        "client": ClientResponse.model_validate(client)
    }


@router.post("/sync/server/{server_id}")
async def sync_server_clients(
    server_id: int,
    current_user: User = Depends(get_current_user)
):
    """Sync clients from server Xray configuration to local database."""
    sync_service = ClientSyncService()
    result = await sync_service.sync_server_clients(server_id)
    
    if not result.get("success"):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result.get("error", "Sync failed")
        )
    
    logger.info(f"Client sync triggered for server {server_id} by user {current_user.username}")
    return result


@router.post("/sync/all")
async def sync_all_servers_clients(
    current_user: User = Depends(get_current_user)
):
    """Sync clients from all active servers to local database."""
    sync_service = ClientSyncService()
    result = await sync_service.sync_all_servers()
    
    logger.info(f"Full client sync triggered by user {current_user.username}")
    return result


@router.get("/sync/compare/{server_id}")
async def compare_server_clients(
    server_id: int,
    current_user: User = Depends(get_current_user)
):
    """Compare clients between server configuration and local database."""
    sync_service = ClientSyncService()
    result = await sync_service.compare_server_clients(server_id)
    
    if not result.get("success"):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result.get("error", "Comparison failed")
        )
    
    return result


@router.get("/sync/analyze/{server_id}")
async def analyze_invalid_clients(
    server_id: int,
    current_user: User = Depends(get_current_user)
):
    """Analyze invalid clients in server configuration."""
    sync_service = ClientSyncService()
    result = await sync_service.analyze_invalid_clients(server_id)
    
    if not result.get("success"):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result.get("error", "Analysis failed")
        )
    
    return result


@router.post("/sync/cleanup/{server_id}")
async def cleanup_orphaned_clients(
    server_id: int,
    current_user: User = Depends(get_current_user)
):
    """Remove clients from database that don't exist in server configuration."""
    sync_service = ClientSyncService()
    result = await sync_service.cleanup_orphaned_clients(server_id)
    
    if not result.get("success"):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result.get("error", "Cleanup failed")
        )
    
    logger.info(f"Client cleanup triggered for server {server_id} by user {current_user.username}")
    return result
