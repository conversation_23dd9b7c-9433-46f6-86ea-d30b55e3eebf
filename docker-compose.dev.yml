# Development override for docker-compose.yml
# Use with: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

version: '3.8'

services:
  vpsscripthelper:
    build:
      args:
        - ENVIRONMENT=development
    environment:
      # Development settings
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=INFO
    
    # For development, we can bind mount the logs directory for easier access
    volumes:
      # Override the named volume with a bind mount for development
      - ./logs:/app/logs
      - ./data:/app/data
      - ./uploads:/app/uploads
      - ./backups:/app/backups
      
      # Mount source code for development (optional)
      # - .:/app
    
    # Development command with auto-reload
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
