# Simple Docker Hub Push Script
# Build and push VPSScriptHelper-BlueBlue to Docker Hub

param(
    [string]$Username = "",
    [string]$Repository = "vpsscripthelper-blueblue", 
    [string]$Tag = "latest"
)

Write-Host "=== VPSScriptHelper-BlueBlue Docker Hub Push ===" -ForegroundColor Cyan
Write-Host "Note: This will rebuild the image with updated dependencies" -ForegroundColor Yellow

# Get username if not provided
if ([string]::IsNullOrEmpty($Username)) {
    $Username = Read-Host "Enter your Docker Hub username"
}

if ([string]::IsNullOrEmpty($Username)) {
    Write-Host "Error: Username is required" -ForegroundColor Red
    exit 1
}

$ImageName = "$Username/$Repository:$Tag"

# Check if Docker is running
Write-Host "Checking Docker..." -ForegroundColor Yellow
try {
    docker info | Out-Null
    Write-Host "✓ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Check if we're in the right directory
if (-not (Test-Path "main.py")) {
    Write-Host "✗ main.py not found. Run this script from the project root." -ForegroundColor Red
    exit 1
}

# Login to Docker Hub
Write-Host "Logging into Docker Hub..." -ForegroundColor Yellow
try {
    docker login
    Write-Host "✓ Logged into Docker Hub" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to login to Docker Hub" -ForegroundColor Red
    exit 1
}

# Build image (force rebuild to include email-validator)
Write-Host "Building Docker image: $ImageName" -ForegroundColor Yellow
Write-Host "This may take a few minutes..." -ForegroundColor Gray
try {
    docker build --no-cache --platform linux/amd64 -t $ImageName .
    Write-Host "✓ Image built successfully" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to build image" -ForegroundColor Red
    exit 1
}

# Push to Docker Hub
Write-Host "Pushing to Docker Hub..." -ForegroundColor Yellow
try {
    docker push $ImageName
    Write-Host "✓ Successfully pushed to Docker Hub" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to push to Docker Hub" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== SUCCESS ===" -ForegroundColor Green
Write-Host "Image: $ImageName" -ForegroundColor Cyan
Write-Host "Docker Hub: https://hub.docker.com/r/$Username/$Repository" -ForegroundColor Cyan
Write-Host "`nTo run on Linux:" -ForegroundColor Yellow
Write-Host "docker run -d -p 8000:8000 --name vpsscripthelper $ImageName" -ForegroundColor White
Write-Host "`nTo update existing container:" -ForegroundColor Yellow
Write-Host "docker stop vpsscripthelper && docker rm vpsscripthelper" -ForegroundColor White
Write-Host "docker run -d -p 8000:8000 --name vpsscripthelper $ImageName" -ForegroundColor White
