@echo off
REM Test script for Docker logging permissions (Windows version)
REM This script builds and tests the Docker container to ensure logging works properly

echo 🐳 Testing Docker Logging Permissions
echo ======================================

REM Function to cleanup containers and volumes
:cleanup
echo 🧹 Cleaning up test containers and volumes...

REM Stop and remove test container
docker stop vpsscripthelper-test >nul 2>&1
docker rm vpsscripthelper-test >nul 2>&1

REM Remove test volumes
docker volume rm vpsscripthelper-test-logs >nul 2>&1
docker volume rm vpsscripthelper-test-data >nul 2>&1

echo ✅ Cleanup completed
goto :eof

REM Step 1: Build the Docker image
echo 🔨 Building Docker image...
docker build -t vpsscripthelper-blueblue:test .

if %errorlevel% neq 0 (
    echo ❌ Docker image build failed
    exit /b 1
)

echo ✅ Docker image built successfully

REM Step 2: Test with named volumes (production-like)
echo 🧪 Testing with named volumes (production-like)...

docker run -d ^
    --name vpsscripthelper-test ^
    -p 8001:8000 ^
    -v vpsscripthelper-test-logs:/app/logs ^
    -v vpsscripthelper-test-data:/app/data ^
    -e ENVIRONMENT=development ^
    -e DEBUG=true ^
    -e LOG_LEVEL=INFO ^
    vpsscripthelper-blueblue:test

REM Wait for container to start
echo ⏳ Waiting for container to start...
timeout /t 10 /nobreak >nul

REM Check if container is running
docker ps | findstr vpsscripthelper-test >nul
if %errorlevel% neq 0 (
    echo ❌ Container failed to start
    echo 📋 Container logs:
    docker logs vpsscripthelper-test
    call :cleanup
    exit /b 1
)

echo ✅ Container is running

REM Check container logs for permission issues
echo 📋 Checking container logs for permission issues...
docker logs vpsscripthelper-test 2>&1 | findstr /i "permission error warning"

REM Test if logs directory is writable
echo 🔍 Testing log file creation...
docker exec vpsscripthelper-test ls -la /app/logs/
docker exec vpsscripthelper-test touch /app/logs/test-write.log

if %errorlevel% equ 0 (
    echo ✅ Log directory is writable
    docker exec vpsscripthelper-test rm -f /app/logs/test-write.log
) else (
    echo ❌ Log directory is not writable
)

REM Test API endpoint
echo 🌐 Testing API endpoint...
timeout /t 5 /nobreak >nul

REM Use PowerShell for HTTP request (more reliable than curl on Windows)
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8001/api/v1/health' -UseBasicParsing; $response.StatusCode } catch { 0 }" > temp_response.txt
set /p response=<temp_response.txt
del temp_response.txt

if "%response%"=="200" (
    echo ✅ API endpoint is responding
) else (
    echo ❌ API endpoint is not responding (HTTP %response%^)
    echo 📋 Recent container logs:
    docker logs --tail 20 vpsscripthelper-test
)

REM Show final container status
echo 📊 Final container status:
docker exec vpsscripthelper-test ps aux
docker exec vpsscripthelper-test ls -la /app/logs/

echo 🎉 Docker logging test completed!
echo 💡 To run the container manually:
echo    docker run -p 8000:8000 -v logs:/app/logs vpsscripthelper-blueblue:test

REM Cleanup
call :cleanup
