# Force rebuild and push Docker image
# This script ensures a complete rebuild with all dependencies

param(
    [string]$Username = "",
    [string]$Repository = "vpsscripthelper-blueblue", 
    [string]$Tag = "latest"
)

Write-Host "=== FORCE REBUILD AND PUSH ===" -ForegroundColor Red
Write-Host "This will completely rebuild the Docker image from scratch" -ForegroundColor Yellow

# Get username if not provided
if ([string]::IsNullOrEmpty($Username)) {
    $Username = Read-Host "Enter your Docker Hub username"
}

if ([string]::IsNullOrEmpty($Username)) {
    Write-Host "Error: Username is required" -ForegroundColor Red
    exit 1
}

$ImageName = "$Username/$Repository:$Tag"

# Check if Docker is running
Write-Host "Checking Docker..." -ForegroundColor Yellow
try {
    docker info | Out-Null
    Write-Host "✓ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Check if we're in the right directory
if (-not (Test-Path "main.py")) {
    Write-Host "✗ main.py not found. Run this script from the project root." -ForegroundColor Red
    exit 1
}

# Check requirements.txt contains email-validator
$requirements = Get-Content "requirements.txt"
if ($requirements -match "email-validator") {
    Write-Host "✓ email-validator found in requirements.txt" -ForegroundColor Green
} else {
    Write-Host "✗ email-validator NOT found in requirements.txt" -ForegroundColor Red
    exit 1
}

# Remove existing local image
Write-Host "Removing existing local image..." -ForegroundColor Yellow
try {
    docker rmi $ImageName 2>$null
    Write-Host "✓ Local image removed" -ForegroundColor Green
} catch {
    Write-Host "! No existing local image to remove" -ForegroundColor Gray
}

# Clean Docker build cache
Write-Host "Cleaning Docker build cache..." -ForegroundColor Yellow
try {
    docker builder prune -f | Out-Null
    Write-Host "✓ Build cache cleaned" -ForegroundColor Green
} catch {
    Write-Host "! Could not clean build cache" -ForegroundColor Gray
}

# Login to Docker Hub
Write-Host "Logging into Docker Hub..." -ForegroundColor Yellow
try {
    docker login
    Write-Host "✓ Logged into Docker Hub" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to login to Docker Hub" -ForegroundColor Red
    exit 1
}

# Build image with verbose output
Write-Host "Building Docker image: $ImageName" -ForegroundColor Yellow
Write-Host "Using --no-cache to ensure fresh build..." -ForegroundColor Gray
Write-Host "This will take several minutes..." -ForegroundColor Gray

try {
    # Build with verbose output to see what's happening
    docker build --no-cache --platform linux/amd64 -t $ImageName . --progress=plain
    Write-Host "✓ Image built successfully" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to build image" -ForegroundColor Red
    Write-Host "Check the build output above for errors" -ForegroundColor Red
    exit 1
}

# Verify email-validator is installed in the image
Write-Host "Verifying email-validator is installed..." -ForegroundColor Yellow
try {
    $result = docker run --rm $ImageName pip list | Select-String "email-validator"
    if ($result) {
        Write-Host "✓ email-validator is installed in the image: $result" -ForegroundColor Green
    } else {
        Write-Host "✗ email-validator NOT found in the image" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "! Could not verify email-validator installation" -ForegroundColor Yellow
}

# Push to Docker Hub
Write-Host "Pushing to Docker Hub..." -ForegroundColor Yellow
try {
    docker push $ImageName
    Write-Host "✓ Successfully pushed to Docker Hub" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to push to Docker Hub" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== SUCCESS ===" -ForegroundColor Green
Write-Host "Image: $ImageName" -ForegroundColor Cyan
Write-Host "Docker Hub: https://hub.docker.com/r/$Username/$Repository" -ForegroundColor Cyan
Write-Host "`nTo update your Linux container:" -ForegroundColor Yellow
Write-Host "docker stop vpsscripthelper && docker rm vpsscripthelper" -ForegroundColor White
Write-Host "docker rmi $ImageName" -ForegroundColor White
Write-Host "docker pull $ImageName" -ForegroundColor White
Write-Host "docker run -d -p 8000:8000 --name vpsscripthelper $ImageName" -ForegroundColor White
