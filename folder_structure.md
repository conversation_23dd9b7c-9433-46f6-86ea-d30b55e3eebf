# VPSScriptHelper-BlueBlue Folder Structure

```
VPSScriptHelper-BlueBlue/
├── README.md                           # Project documentation
├── requirements.txt                    # Python dependencies
├── folder_structure.md                 # This file - project structure documentation
├── main.py                            # FastAPI application entry point
├── .env.example                       # Environment variables template
├── .gitignore                         # Git ignore file
├── HOW_TO_ADD_SERVERS.md              # Server management guide
├── HOW_TO_MANAGE_CLIENTS.md           # Client management guide
├── CLIENT_SYNC_TROUBLESHOOTING.md     # Client sync troubleshooting guide
├── DEVELOPMENT_AUTH.md                # Development authentication guide
├── DOCKER_SIMPLE.md                   # Simple Docker deployment guide
├── Dockerfile                         # Docker container configuration
├── .dockerignore                      # Docker build ignore file
├── docker-compose.yml                 # Docker Compose configuration
├── simple-push.ps1                    # Simple PowerShell push script
├── push-to-dockerhub.bat              # Simple Windows batch script
├── run-on-linux.sh                   # Linux deployment script
├── SETUP.md                           # Setup and installation guide
│
├── app/                               # Main application directory
│   ├── __init__.py
│   ├── core/                          # Core application configuration
│   │   ├── __init__.py
│   │   ├── config.py                  # Application settings
│   │   ├── database.py                # Database configuration
│   │   ├── security.py                # Authentication and security
│   │   └── exceptions.py              # Custom exceptions
│   │
│   ├── models/                        # SQLAlchemy models
│   │   ├── __init__.py
│   │   ├── server.py                  # Server model
│   │   ├── client.py                  # VPN client model
│   │   ├── user.py                    # Application user model
│   │   └── task.py                    # Scheduled task model
│   │
│   ├── schemas/                       # Pydantic schemas for API
│   │   ├── __init__.py
│   │   ├── server.py                  # Server schemas ✅
│   │   ├── client.py                  # Client schemas
│   │   ├── user.py                    # User schemas ✅
│   │   └── task.py                    # Task schemas
│   │
│   ├── controllers/                   # API route controllers
│   │   ├── __init__.py
│   │   ├── auth.py                    # Authentication endpoints
│   │   ├── servers.py                 # Server management endpoints
│   │   ├── clients.py                 # Client management endpoints
│   │   ├── config.py                  # Configuration endpoints
│   │   ├── tasks.py                   # Task management endpoints
│   │   └── health.py                  # Health check endpoints
│   │
│   ├── services/                      # Business logic services
│   │   ├── __init__.py
│   │   ├── ssh_service.py             # SSH/SFTP operations
│   │   ├── config_service.py          # Xray config management
│   │   ├── client_service.py          # Client management logic
│   │   ├── client_sync_service.py     # Client synchronization service
│   │   ├── server_service.py          # Server management logic
│   │   ├── expiry_service.py          # Client expiry checking
│   │   └── task_service.py            # Scheduled task management
│   │
│   ├── utils/                         # Utility functions
│   │   ├── __init__.py
│   │   ├── date_utils.py              # Date parsing and validation
│   │   ├── crypto_utils.py            # Encryption utilities
│   │   ├── validation.py              # Data validation helpers
│   │   └── logger.py                  # Logging configuration
│   │
│   └── dependencies.py                # FastAPI dependencies
│
├── alembic/                           # Database migrations
│   ├── versions/
│   ├── env.py
│   ├── script.py.mako
│   └── alembic.ini
│
├── tests/                             # Test files
│   ├── __init__.py
│   ├── conftest.py                    # Test configuration
│   ├── test_auth.py                   # Authentication tests
│   ├── test_servers.py                # Server management tests
│   ├── test_clients.py                # Client management tests
│   └── test_services.py               # Service layer tests
│
├── config/                            # Configuration files
│   └── xray_template.json             # Xray config template
│
├── logs/                              # Application logs (created at runtime)
│   ├── app.log                        # General application logs
│   └── error.log                      # Error logs
│
├── uploads/                           # File uploads (created at runtime)
│
├── backups/                           # Configuration backups (created at runtime)
│
└── scripts/                           # Utility scripts
    ├── init_db.py                     # Database initialization
    ├── backup_configs.py              # Configuration backup
    └── migrate_data.py                # Data migration scripts
```

## Architecture Overview

### MVC Pattern Implementation:
- **Models** (`app/models/`): SQLAlchemy ORM models for database entities
- **Views** (`app/controllers/`): FastAPI route handlers (API endpoints)
- **Controllers** (`app/services/`): Business logic and service layer

### Key Components:
1. **Core**: Application configuration, database setup, security
2. **Models**: Database schema definitions
3. **Schemas**: Pydantic models for API request/response validation
4. **Controllers**: API endpoint definitions and routing
5. **Services**: Business logic implementation
6. **Utils**: Helper functions and utilities
7. **Dependencies**: FastAPI dependency injection

### Design Principles:
- **Separation of Concerns**: Clear separation between API, business logic, and data layers
- **Dependency Injection**: Using FastAPI's dependency system
- **Configuration Management**: Environment-based configuration
- **Error Handling**: Centralized exception handling
- **Testing**: Comprehensive test coverage
- **Logging**: Structured logging throughout the application
- **Security**: JWT authentication and secure SSH connections
