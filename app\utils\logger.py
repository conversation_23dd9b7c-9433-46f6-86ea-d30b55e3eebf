"""
Logging configuration and utilities
"""

import logging
import logging.config
import sys
from pathlib import Path
from app.core.config import settings


def setup_logging():
    """Setup application logging configuration."""

    # Create logs directory if it doesn't exist and check permissions
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # Check if we can write to the logs directory
    can_write_logs = True
    try:
        test_file = log_dir / "test_write.tmp"
        test_file.write_text("test")
        test_file.unlink()
    except (PermissionError, OSError) as e:
        can_write_logs = False
        print(f"Warning: Cannot write to logs directory: {e}")
        print("Falling back to console-only logging")

    # Base formatters
    formatters = {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        },
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s:%(lineno)d - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        }
    }

    # Base handlers - always include console
    handlers = {
        "console": {
            "class": "logging.StreamHandler",
            "level": settings.LOG_LEVEL,
            "formatter": "default",
            "stream": sys.stdout
        }
    }

    # Add file handlers only if we can write to logs directory
    if can_write_logs:
        handlers.update({
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "detailed",
                "filename": "logs/app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": "logs/error.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            }
        })

    # Determine which handlers to use for each logger
    default_handlers = ["console"]
    if can_write_logs:
        default_handlers.extend(["file", "error_file"])

    error_handlers = ["console"]
    if can_write_logs:
        error_handlers.append("error_file")

    file_handlers = []
    if can_write_logs:
        file_handlers.append("file")
    else:
        file_handlers.append("console")

    # Logging configuration
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": formatters,
        "handlers": handlers,
        "loggers": {
            "": {  # Root logger
                "level": settings.LOG_LEVEL,
                "handlers": default_handlers,
                "propagate": False
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            },
            "uvicorn.error": {
                "level": "INFO",
                "handlers": error_handlers,
                "propagate": False
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            },
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": file_handlers,
                "propagate": False
            }
        }
    }

    # Apply logging configuration
    try:
        logging.config.dictConfig(logging_config)
    except Exception as e:
        # Fallback to basic console logging if configuration fails
        print(f"Warning: Failed to configure logging: {e}")
        print("Falling back to basic console logging")
        logging.basicConfig(
            level=getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO),
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[logging.StreamHandler(sys.stdout)]
        )

    # Set specific log levels for third-party libraries
    logging.getLogger("paramiko").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("apscheduler").setLevel(logging.INFO)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        logging.Logger: Logger instance
    """
    return logging.getLogger(name)
