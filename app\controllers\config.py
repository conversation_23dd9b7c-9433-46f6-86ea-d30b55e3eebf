"""
Configuration management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Dict, Any, List, Optional
import logging
import os
import glob
from datetime import datetime

from app.core.database import get_db
from app.dependencies import get_current_user
from app.models.user import User
from app.models.server import Server
from app.models.client import Client
from app.services.config_service import ConfigService
from app.core.exceptions import ConfigurationError
from app.utils.date_utils import is_date_expired
from app.schemas.config import (
    ConfigOverviewResponse, ConfigOverview, ServerConfigResponse,
    ConfigUpdateRequest, ConfigUpdateResponse, ConfigBackupRequest,
    ConfigBackupResponse, ConfigBackupInfo, ConfigRestoreRequest,
    ConfigRestoreResponse, ConfigValidationResponse, ConfigBackupListResponse,
    ConfigBackupListItem, ConfigServerSummary, ConfigClientSummary,
    ConfigComparisonResult, ConfigSyncRequest, ConfigSyncResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=ConfigOverviewResponse)
async def get_configurations(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get configuration overview for all servers."""
    try:
        config_service = ConfigService()
        
        # Get all active servers
        result = await db.execute(select(Server).where(Server.is_active == True))
        servers = result.scalars().all()
        
        server_configs = []
        total_clients = 0
        total_active_clients = 0
        total_expired_clients = 0
        
        for server in servers:
            try:
                # Get clients from config
                config_clients = config_service.get_clients_from_config(server)
                
                active_count = 0
                expired_count = 0
                
                for client in config_clients:
                    expired_date = client.get("expired_date", "")
                    if is_date_expired(expired_date):
                        expired_count += 1
                    else:
                        active_count += 1
                
                # Check if config is valid
                try:
                    config_service.get_config(server)
                    config_valid = True
                except:
                    config_valid = False
                
                server_config = ConfigOverview(
                    server_id=server.id,
                    server_name=server.name,
                    server_host=server.host,
                    config_path=server.xray_config_path or "/usr/local/etc/xray/config.json",
                    total_clients=len(config_clients),
                    active_clients=active_count,
                    expired_clients=expired_count,
                    config_valid=config_valid,
                    last_updated=server.updated_at
                )
                
                server_configs.append(server_config)
                total_clients += len(config_clients)
                total_active_clients += active_count
                total_expired_clients += expired_count
                
            except Exception as e:
                logger.error(f"Error getting config for server {server.name}: {str(e)}")
                # Add server with error state
                server_config = ConfigOverview(
                    server_id=server.id,
                    server_name=server.name,
                    server_host=server.host,
                    config_path=server.xray_config_path or "/usr/local/etc/xray/config.json",
                    total_clients=0,
                    active_clients=0,
                    expired_clients=0,
                    config_valid=False,
                    last_updated=server.updated_at
                )
                server_configs.append(server_config)
        
        return ConfigOverviewResponse(
            servers=server_configs,
            total_servers=len(servers),
            total_clients=total_clients,
            total_active_clients=total_active_clients,
            total_expired_clients=total_expired_clients
        )
        
    except Exception as e:
        logger.error(f"Error getting configuration overview: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting configuration overview: {str(e)}"
        )


@router.get("/server/{server_id}", response_model=ServerConfigResponse)
async def get_server_config(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get configuration for a specific server."""
    try:
        # Get server
        result = await db.execute(select(Server).where(Server.id == server_id))
        server = result.scalar_one_or_none()
        
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Server not found"
            )
        
        config_service = ConfigService()
        
        try:
            config = config_service.get_config(server)
            clients = config_service.get_clients_from_config(server)
            is_valid = True
        except ConfigurationError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Configuration error: {str(e)}"
            )
        
        return ServerConfigResponse(
            server_id=server.id,
            server_name=server.name,
            config=config,
            clients_count=len(clients),
            last_updated=server.updated_at,
            is_valid=is_valid
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting server config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting server configuration: {str(e)}"
        )


@router.put("/server/{server_id}", response_model=ConfigUpdateResponse)
async def update_server_config(
    server_id: int,
    request: ConfigUpdateRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update configuration for a specific server."""
    try:
        # Get server
        result = await db.execute(select(Server).where(Server.id == server_id))
        server = result.scalar_one_or_none()
        
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Server not found"
            )
        
        config_service = ConfigService()
        backup_path = None
        backup_created = False
        
        # Create backup if requested
        if request.create_backup:
            try:
                backup_created = config_service.backup_config(server)
                if backup_created:
                    backup_path = f"{server.xray_config_path or '/usr/local/etc/xray/config.json'}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            except Exception as e:
                logger.warning(f"Failed to create backup: {str(e)}")
        
        # Update configuration
        try:
            success = config_service.update_config(server, request.config)
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update configuration"
                )
            
            # Update server timestamp
            server.updated_at = datetime.utcnow()
            await db.commit()
            
            return ConfigUpdateResponse(
                success=True,
                message="Configuration updated successfully",
                backup_created=backup_created,
                backup_path=backup_path,
                updated_at=datetime.utcnow()
            )
            
        except ConfigurationError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Configuration error: {str(e)}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating server config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating server configuration: {str(e)}"
        )


@router.post("/backup", response_model=ConfigBackupResponse)
async def backup_configurations(
    request: ConfigBackupRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Backup server configurations."""
    try:
        config_service = ConfigService()
        
        # Get servers to backup
        if request.server_ids:
            result = await db.execute(
                select(Server).where(
                    Server.id.in_(request.server_ids),
                    Server.is_active == True
                )
            )
        else:
            result = await db.execute(select(Server).where(Server.is_active == True))
        
        servers = result.scalars().all()
        
        if not servers:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No servers found to backup"
            )
        
        backups = []
        total_backups = 0
        failed_backups = 0
        
        for server in servers:
            try:
                success = config_service.backup_config(server)
                backup_path = f"{server.xray_config_path or '/usr/local/etc/xray/config.json'}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                backup_info = ConfigBackupInfo(
                    server_id=server.id,
                    server_name=server.name,
                    backup_path=backup_path,
                    created_at=datetime.utcnow(),
                    success=success
                )
                
                if success:
                    total_backups += 1
                else:
                    failed_backups += 1
                    backup_info.error_message = "Backup failed"
                
                backups.append(backup_info)
                
            except Exception as e:
                failed_backups += 1
                backup_info = ConfigBackupInfo(
                    server_id=server.id,
                    server_name=server.name,
                    backup_path="",
                    created_at=datetime.utcnow(),
                    success=False,
                    error_message=str(e)
                )
                backups.append(backup_info)
        
        return ConfigBackupResponse(
            success=failed_backups == 0,
            message=f"Backup completed. {total_backups} successful, {failed_backups} failed",
            backups=backups,
            total_backups=total_backups,
            failed_backups=failed_backups
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error backing up configurations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error backing up configurations: {str(e)}"
        )


@router.post("/restore", response_model=ConfigRestoreResponse)
async def restore_configuration(
    request: ConfigRestoreRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Restore server configuration from backup."""
    try:
        # Get server
        result = await db.execute(select(Server).where(Server.id == request.server_id))
        server = result.scalar_one_or_none()
        
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Server not found"
            )
        
        config_service = ConfigService()
        backup_created = False
        
        # Create backup before restore if requested
        if request.create_backup_before_restore:
            try:
                backup_created = config_service.backup_config(server)
            except Exception as e:
                logger.warning(f"Failed to create backup before restore: {str(e)}")
        
        # Restore configuration
        try:
            config_path = server.xray_config_path or "/usr/local/etc/xray/config.json"
            command = f"sudo cp {request.backup_path} {config_path}"
            
            exit_code, stdout, stderr = config_service.ssh_service.execute_command(server, command)
            
            if exit_code != 0:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to restore configuration: {stderr}"
                )
            
            # Update server timestamp
            server.updated_at = datetime.utcnow()
            await db.commit()
            
            return ConfigRestoreResponse(
                success=True,
                message="Configuration restored successfully",
                backup_created=backup_created,
                restored_at=datetime.utcnow()
            )
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to restore configuration: {str(e)}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error restoring configuration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error restoring configuration: {str(e)}"
        )


@router.get("/validate/server/{server_id}", response_model=ConfigValidationResponse)
async def validate_server_config(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Validate configuration for a specific server."""
    try:
        # Get server
        result = await db.execute(select(Server).where(Server.id == server_id))
        server = result.scalar_one_or_none()
        
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Server not found"
            )
        
        config_service = ConfigService()
        validation_errors = []
        warnings = []
        is_valid = True
        clients_count = 0
        inbounds_count = 0
        
        try:
            config = config_service.get_config(server)
            
            # Validate basic structure
            if "inbounds" not in config:
                validation_errors.append("Missing 'inbounds' section")
                is_valid = False
            else:
                inbounds = config["inbounds"]
                inbounds_count = len(inbounds)
                
                if not isinstance(inbounds, list):
                    validation_errors.append("'inbounds' must be a list")
                    is_valid = False
                elif len(inbounds) == 0:
                    warnings.append("No inbounds configured")
                
                # Check for VLESS protocol
                vless_found = False
                for inbound in inbounds:
                    if inbound.get("protocol") == "vless":
                        vless_found = True
                        clients = inbound.get("settings", {}).get("clients", [])
                        clients_count += len(clients)
                        
                        # Validate clients
                        for i, client in enumerate(clients):
                            if "id" not in client:
                                validation_errors.append(f"Client {i+1} missing 'id' field")
                                is_valid = False
                            if "email" not in client:
                                validation_errors.append(f"Client {i+1} missing 'email' field")
                                is_valid = False
                            if "expired_date" not in client:
                                warnings.append(f"Client {i+1} missing 'expired_date' field")
                
                if not vless_found:
                    warnings.append("No VLESS inbound found")
            
            if "log" not in config:
                warnings.append("Missing 'log' section")
            
        except ConfigurationError as e:
            validation_errors.append(str(e))
            is_valid = False
        except Exception as e:
            validation_errors.append(f"Unexpected error: {str(e)}")
            is_valid = False
        
        return ConfigValidationResponse(
            server_id=server.id,
            server_name=server.name,
            is_valid=is_valid,
            validation_errors=validation_errors,
            warnings=warnings,
            clients_count=clients_count,
            inbounds_count=inbounds_count,
            validated_at=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating server config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error validating server configuration: {str(e)}"
        )


@router.get("/backups", response_model=ConfigBackupListResponse)
async def list_config_backups(
    server_id: Optional[int] = Query(None, description="Filter by server ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """List available configuration backups."""
    try:
        config_service = ConfigService()
        
        # Get servers
        if server_id:
            result = await db.execute(select(Server).where(Server.id == server_id))
            servers = [result.scalar_one_or_none()]
            if not servers[0]:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Server not found"
                )
        else:
            result = await db.execute(select(Server).where(Server.is_active == True))
            servers = result.scalars().all()
        
        all_backups = []
        total_size = 0
        
        for server in servers:
            try:
                config_path = server.xray_config_path or "/usr/local/etc/xray/config.json"
                backup_pattern = f"{config_path}.backup.*"
                
                # List backup files on server
                command = f"ls -la {backup_pattern} 2>/dev/null || echo 'No backups found'"
                exit_code, stdout, stderr = config_service.ssh_service.execute_command(server, command)
                
                if exit_code == 0 and "No backups found" not in stdout:
                    lines = stdout.strip().split('\n')
                    for line in lines:
                        if line and not line.startswith('total'):
                            parts = line.split()
                            if len(parts) >= 9:
                                file_size = int(parts[4])
                                file_path = parts[-1]
                                file_name = os.path.basename(file_path)
                                
                                # Extract timestamp from filename
                                try:
                                    timestamp_str = file_name.split('.backup.')[-1]
                                    created_at = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
                                except:
                                    created_at = datetime.utcnow()
                                
                                backup_item = ConfigBackupListItem(
                                    server_id=server.id,
                                    server_name=server.name,
                                    backup_path=file_path,
                                    backup_name=file_name,
                                    file_size=file_size,
                                    created_at=created_at,
                                    is_accessible=True
                                )
                                
                                all_backups.append(backup_item)
                                total_size += file_size
                
            except Exception as e:
                logger.error(f"Error listing backups for server {server.name}: {str(e)}")
        
        # Sort by creation date (newest first)
        all_backups.sort(key=lambda x: x.created_at, reverse=True)
        
        return ConfigBackupListResponse(
            backups=all_backups,
            total_backups=len(all_backups),
            total_size=total_size
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing config backups: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing configuration backups: {str(e)}"
        )


@router.get("/compare/server/{server_id}", response_model=ConfigComparisonResult)
async def compare_server_config(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Compare server configuration with database clients."""
    try:
        # Get server
        result = await db.execute(select(Server).where(Server.id == server_id))
        server = result.scalar_one_or_none()
        
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Server not found"
            )
        
        config_service = ConfigService()
        
        # Get clients from config
        config_clients_raw = config_service.get_clients_from_config(server)
        config_clients = []
        
        for client in config_clients_raw:
            expired_date = client.get("expired_date", "")
            config_clients.append(ConfigClientSummary(
                client_id=client.get("id", ""),
                email=client.get("email", ""),
                shopee_username=client.get("shopeeUsername"),
                expired_date=expired_date,
                is_expired=is_date_expired(expired_date)
            ))
        
        # Get clients from database
        db_result = await db.execute(
            select(Client).where(Client.server_id == server_id)
        )
        db_clients_raw = db_result.scalars().all()
        database_clients = []
        
        for client in db_clients_raw:
            database_clients.append(ConfigClientSummary(
                client_id=client.client_id,
                email=client.email,
                shopee_username=client.shopee_username,
                expired_date=client.expired_date,
                is_expired=client.is_expired
            ))
        
        # Compare clients
        config_ids = {c.client_id for c in config_clients}
        db_ids = {c.client_id for c in database_clients}
        
        only_in_config = [c for c in config_clients if c.client_id not in db_ids]
        only_in_database = [c for c in database_clients if c.client_id not in config_ids]
        
        # Find mismatched clients (same ID but different data)
        mismatched = []
        for config_client in config_clients:
            if config_client.client_id in db_ids:
                db_client = next(c for c in database_clients if c.client_id == config_client.client_id)
                if (config_client.email != db_client.email or 
                    config_client.expired_date != db_client.expired_date):
                    mismatched.append({
                        "client_id": config_client.client_id,
                        "config_data": config_client.dict(),
                        "database_data": db_client.dict()
                    })
        
        in_sync = len(only_in_config) == 0 and len(only_in_database) == 0 and len(mismatched) == 0
        
        return ConfigComparisonResult(
            server_id=server.id,
            server_name=server.name,
            config_clients=config_clients,
            database_clients=database_clients,
            only_in_config=only_in_config,
            only_in_database=only_in_database,
            mismatched=mismatched,
            in_sync=in_sync
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error comparing server config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error comparing server configuration: {str(e)}"
        )


@router.post("/sync", response_model=ConfigSyncResponse)
async def sync_configurations(
    request: ConfigSyncRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Sync configurations between servers and database."""
    try:
        config_service = ConfigService()
        
        # Get servers to sync
        if request.server_ids:
            result = await db.execute(
                select(Server).where(
                    Server.id.in_(request.server_ids),
                    Server.is_active == True
                )
            )
        else:
            result = await db.execute(select(Server).where(Server.is_active == True))
        
        servers = result.scalars().all()
        
        if not servers:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No servers found to sync"
            )
        
        synced_servers = []
        failed_servers = []
        total_synced_clients = 0
        total_removed_clients = 0
        sync_details = []
        
        for server in servers:
            try:
                if request.sync_direction == "config_to_db":
                    # Sync from config to database (import clients)
                    from app.services.client_service import ClientService
                    client_service = ClientService()
                    
                    sync_result = await client_service.sync_clients_from_server(db, server.id)
                    
                    synced_servers.append(server.id)
                    total_synced_clients += sync_result.get("imported_count", 0)
                    
                    sync_details.append({
                        "server_id": server.id,
                        "server_name": server.name,
                        "action": "config_to_db",
                        "synced_clients": sync_result.get("imported_count", 0),
                        "errors": sync_result.get("errors", [])
                    })
                    
                elif request.sync_direction == "db_to_config":
                    # Sync from database to config (export clients)
                    db_result = await db.execute(
                        select(Client).where(
                            Client.server_id == server.id,
                            Client.is_active == True
                        )
                    )
                    db_clients = db_result.scalars().all()
                    
                    synced_count = 0
                    for client in db_clients:
                        success = config_service.add_client_to_config(server, client)
                        if success:
                            synced_count += 1
                    
                    synced_servers.append(server.id)
                    total_synced_clients += synced_count
                    
                    sync_details.append({
                        "server_id": server.id,
                        "server_name": server.name,
                        "action": "db_to_config",
                        "synced_clients": synced_count,
                        "total_clients": len(db_clients)
                    })
                
                # Remove orphaned entries if requested
                if request.remove_orphaned:
                    if request.sync_direction == "config_to_db":
                        # Remove database clients not in config
                        config_clients = config_service.get_clients_from_config(server)
                        config_ids = {c.get("id") for c in config_clients}
                        
                        db_result = await db.execute(
                            select(Client).where(Client.server_id == server.id)
                        )
                        db_clients = db_result.scalars().all()
                        
                        removed_count = 0
                        for db_client in db_clients:
                            if db_client.client_id not in config_ids:
                                await db.delete(db_client)
                                removed_count += 1
                        
                        total_removed_clients += removed_count
                        
                    elif request.sync_direction == "db_to_config":
                        # Remove config clients not in database
                        db_result = await db.execute(
                            select(Client).where(Client.server_id == server.id)
                        )
                        db_clients = db_result.scalars().all()
                        db_ids = {c.client_id for c in db_clients}
                        
                        config_clients = config_service.get_clients_from_config(server)
                        removed_count = 0
                        
                        for config_client in config_clients:
                            client_id = config_client.get("id")
                            if client_id not in db_ids:
                                success = config_service.remove_client_from_config(server, client_id)
                                if success:
                                    removed_count += 1
                        
                        total_removed_clients += removed_count
                
            except Exception as e:
                logger.error(f"Error syncing server {server.name}: {str(e)}")
                failed_servers.append(server.id)
                sync_details.append({
                    "server_id": server.id,
                    "server_name": server.name,
                    "action": request.sync_direction,
                    "error": str(e)
                })
        
        # Commit database changes
        await db.commit()
        
        return ConfigSyncResponse(
            success=len(failed_servers) == 0,
            message=f"Sync completed. {len(synced_servers)} servers synced, {len(failed_servers)} failed",
            synced_servers=synced_servers,
            failed_servers=failed_servers,
            total_synced_clients=total_synced_clients,
            total_removed_clients=total_removed_clients,
            sync_details=sync_details
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error syncing configurations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error syncing configurations: {str(e)}"
        )
