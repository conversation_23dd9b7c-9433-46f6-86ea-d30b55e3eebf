"""
Configuration-related Pydantic schemas
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class ConfigOverview(BaseModel):
    """Schema for configuration overview."""
    server_id: int
    server_name: str
    server_host: str
    config_path: str
    total_clients: int
    active_clients: int
    expired_clients: int
    last_backup: Optional[datetime] = None
    config_valid: bool
    last_updated: Optional[datetime] = None


class ConfigOverviewResponse(BaseModel):
    """Schema for configuration overview response."""
    servers: List[ConfigOverview]
    total_servers: int
    total_clients: int
    total_active_clients: int
    total_expired_clients: int


class ServerConfigResponse(BaseModel):
    """Schema for server configuration response."""
    server_id: int
    server_name: str
    config: Dict[str, Any]
    clients_count: int
    last_updated: Optional[datetime] = None
    is_valid: bool


class ConfigUpdateRequest(BaseModel):
    """Schema for configuration update request."""
    config: Dict[str, Any] = Field(..., description="Xray configuration JSON")
    create_backup: bool = Field(True, description="Create backup before update")


class ConfigUpdateResponse(BaseModel):
    """Schema for configuration update response."""
    success: bool
    message: str
    backup_created: bool
    backup_path: Optional[str] = None
    updated_at: datetime


class ConfigBackupRequest(BaseModel):
    """Schema for configuration backup request."""
    server_ids: Optional[List[int]] = Field(None, description="Specific server IDs to backup, or None for all")
    description: Optional[str] = Field(None, description="Backup description")


class ConfigBackupInfo(BaseModel):
    """Schema for backup information."""
    server_id: int
    server_name: str
    backup_path: str
    backup_size: Optional[int] = None
    created_at: datetime
    success: bool
    error_message: Optional[str] = None


class ConfigBackupResponse(BaseModel):
    """Schema for configuration backup response."""
    success: bool
    message: str
    backups: List[ConfigBackupInfo]
    total_backups: int
    failed_backups: int


class ConfigRestoreRequest(BaseModel):
    """Schema for configuration restore request."""
    server_id: int
    backup_path: str
    create_backup_before_restore: bool = Field(True, description="Create backup before restore")


class ConfigRestoreResponse(BaseModel):
    """Schema for configuration restore response."""
    success: bool
    message: str
    backup_created: bool
    restored_at: datetime


class ConfigValidationResponse(BaseModel):
    """Schema for configuration validation response."""
    server_id: int
    server_name: str
    is_valid: bool
    validation_errors: List[str]
    warnings: List[str]
    clients_count: int
    inbounds_count: int
    validated_at: datetime


class ConfigBackupListItem(BaseModel):
    """Schema for backup list item."""
    server_id: int
    server_name: str
    backup_path: str
    backup_name: str
    file_size: int
    created_at: datetime
    is_accessible: bool


class ConfigBackupListResponse(BaseModel):
    """Schema for backup list response."""
    backups: List[ConfigBackupListItem]
    total_backups: int
    total_size: int


class ConfigClientSummary(BaseModel):
    """Schema for client summary in config."""
    client_id: str
    email: str
    shopee_username: Optional[str] = None
    expired_date: str
    is_expired: bool


class ConfigServerSummary(BaseModel):
    """Schema for server configuration summary."""
    server_id: int
    server_name: str
    config_path: str
    clients: List[ConfigClientSummary]
    total_clients: int
    active_clients: int
    expired_clients: int
    config_size: Optional[int] = None
    last_modified: Optional[datetime] = None


class ConfigComparisonResult(BaseModel):
    """Schema for configuration comparison result."""
    server_id: int
    server_name: str
    config_clients: List[ConfigClientSummary]
    database_clients: List[ConfigClientSummary]
    only_in_config: List[ConfigClientSummary]
    only_in_database: List[ConfigClientSummary]
    mismatched: List[Dict[str, Any]]
    in_sync: bool


class ConfigSyncRequest(BaseModel):
    """Schema for configuration sync request."""
    server_ids: Optional[List[int]] = Field(None, description="Specific server IDs to sync, or None for all")
    sync_direction: str = Field("config_to_db", description="Direction: 'config_to_db' or 'db_to_config'")
    remove_orphaned: bool = Field(False, description="Remove orphaned entries")


class ConfigSyncResponse(BaseModel):
    """Schema for configuration sync response."""
    success: bool
    message: str
    synced_servers: List[int]
    failed_servers: List[int]
    total_synced_clients: int
    total_removed_clients: int
    sync_details: List[Dict[str, Any]] 