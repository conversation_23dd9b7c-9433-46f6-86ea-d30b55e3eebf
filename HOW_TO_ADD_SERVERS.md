# How to Add Servers to VPSScriptHelper-BlueBlue

This guide explains how to add BlueBlue VPN servers to your VPSScriptHelper-BlueBlue management system.

## Prerequisites

1. **Application Running**: Ensure the application is running
   ```bash
   uvicorn main:app --reload
   ```

2. **Authentication**: You need to be logged in (or use development mode)

## Method 1: Using the API Documentation (Recommended)

### Step 1: Access API Documentation
Open your browser and go to: `http://localhost:8000/docs`

### Step 2: Authenticate
1. Click the **"Authorize"** button at the top right
2. Login with your credentials:
   - Username: `admin`
   - Password: `admin123` (change this after first login!)

### Step 3: Add Server
1. Find the **"Servers"** section
2. Click on **"POST /api/v1/servers/"**
3. Click **"Try it out"**
4. Fill in the server details in the JSON format:

```json
{
  "name": "My VPS Server",
  "host": "*************",
  "port": 22,
  "username": "root",
  "password": "your_server_password",
  "description": "Main production server",
  "xray_config_path": "/etc/xray/config.json",
  "xray_service_name": "xray",
  "is_active": true,
  "tags": ["production", "main"]
}
```

5. Click **"Execute"**

## Method 2: Using curl Commands

### Step 1: Get Authentication Token
```bash
TOKEN=$(curl -s -X POST "http://localhost:8000/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "admin123"}' \
     | jq -r '.access_token')

echo "Token: $TOKEN"
```

### Step 2: Add Server with Password Authentication
```bash
curl -X POST "http://localhost:8000/api/v1/servers/" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Production Server",
       "host": "*************",
       "port": 22,
       "username": "root",
       "password": "your_password",
       "description": "Main production server",
       "is_active": true,
       "tags": ["production"]
     }'
```

### Step 3: Add Server with SSH Key Authentication
```bash
curl -X POST "http://localhost:8000/api/v1/servers/" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Secure Server",
       "host": "*************",
       "port": 22,
       "username": "root",
       "private_key": "-----BEGIN OPENSSH PRIVATE KEY-----\nYOUR_PRIVATE_KEY_CONTENT_HERE\n-----END OPENSSH PRIVATE KEY-----",
       "private_key_passphrase": "optional_passphrase",
       "description": "Secure server with SSH key",
       "is_active": true,
       "tags": ["secure", "production"]
     }'
```

## Method 3: Development Mode (No Authentication)

If you have development mode enabled (`DISABLE_AUTH_IN_DEV=true`), you can skip authentication:

```bash
# No token needed in development mode
curl -X POST "http://localhost:8000/api/v1/servers/" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Dev Server",
       "host": "*************",
       "port": 22,
       "username": "root",
       "password": "dev_password",
       "description": "Development server",
       "is_active": false,
       "tags": ["development"]
     }'
```

## Server Configuration Fields

### Required Fields
- **name**: Unique server name
- **host**: Server IP address or hostname
- **username**: SSH username
- **password** OR **private_key**: Authentication method

### Optional Fields
- **port**: SSH port (default: 22)
- **private_key_passphrase**: If private key is encrypted
- **description**: Server description
- **xray_config_path**: Path to Xray config (default: `/etc/xray/config.json`)
- **xray_service_name**: Xray service name (default: `xray`)
- **is_active**: Whether server is active (default: `true`)
- **tags**: List of tags for organization

## Authentication Methods

### 1. Password Authentication
```json
{
  "name": "Server Name",
  "host": "*************",
  "username": "root",
  "password": "your_password"
}
```

### 2. SSH Key Authentication
```json
{
  "name": "Server Name",
  "host": "*************",
  "username": "root",
  "private_key": "-----BEGIN OPENSSH PRIVATE KEY-----\n...\n-----END OPENSSH PRIVATE KEY-----"
}
```

### 3. SSH Key with Passphrase
```json
{
  "name": "Server Name",
  "host": "*************",
  "username": "root",
  "private_key": "-----BEGIN OPENSSH PRIVATE KEY-----\n...\n-----END OPENSSH PRIVATE KEY-----",
  "private_key_passphrase": "your_passphrase"
}
```

## Testing Server Connection

After adding a server, test the connection:

```bash
# Test connection
curl -X POST "http://localhost:8000/api/v1/servers/1/test-connection" \
     -H "Authorization: Bearer $TOKEN"

# Expected response:
{
  "success": true,
  "connection_time": 0.85,
  "message": "Connection successful",
  "server_info": {
    "hostname": "*************",
    "port": 22,
    "username": "root"
  }
}
```

## Managing Servers

### List All Servers
```bash
curl -X GET "http://localhost:8000/api/v1/servers/" \
     -H "Authorization: Bearer $TOKEN"
```

### Get Specific Server
```bash
curl -X GET "http://localhost:8000/api/v1/servers/1" \
     -H "Authorization: Bearer $TOKEN"
```

### Update Server
```bash
curl -X PUT "http://localhost:8000/api/v1/servers/1" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "description": "Updated description",
       "is_active": true
     }'
```

### Delete Server
```bash
curl -X DELETE "http://localhost:8000/api/v1/servers/1" \
     -H "Authorization: Bearer $TOKEN"
```

## Server Management Operations

### Check Xray Service Status
```bash
curl -X GET "http://localhost:8000/api/v1/servers/1/service-status" \
     -H "Authorization: Bearer $TOKEN"
```

### Restart Xray Service
```bash
curl -X POST "http://localhost:8000/api/v1/servers/1/restart-xray" \
     -H "Authorization: Bearer $TOKEN"
```

### Get Server Configuration
```bash
curl -X GET "http://localhost:8000/api/v1/servers/1/config" \
     -H "Authorization: Bearer $TOKEN"
```

### Get Clients from Server
```bash
curl -X GET "http://localhost:8000/api/v1/servers/1/clients" \
     -H "Authorization: Bearer $TOKEN"
```

### Remove Expired Clients
```bash
curl -X POST "http://localhost:8000/api/v1/servers/1/remove-expired" \
     -H "Authorization: Bearer $TOKEN"
```

## Python Script Example

Here's a Python script to add servers programmatically:

```python
#!/usr/bin/env python3
import httpx
import asyncio
import json

class ServerManager:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.token = None

    async def login(self, username, password):
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/auth/login",
                json={"username": username, "password": password}
            )
            if response.status_code == 200:
                self.token = response.json()["access_token"]
                return True
            return False

    async def add_server(self, server_data):
        headers = {"Authorization": f"Bearer {self.token}"}
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/servers/",
                headers=headers,
                json=server_data
            )
            return response.json()

    async def test_connection(self, server_id):
        headers = {"Authorization": f"Bearer {self.token}"}
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/servers/{server_id}/test-connection",
                headers=headers
            )
            return response.json()

async def main():
    manager = ServerManager()
    
    # Login
    if await manager.login("admin", "admin123"):
        print("✅ Login successful")
        
        # Add server
        server_data = {
            "name": "Auto-Added Server",
            "host": "*************",
            "port": 22,
            "username": "root",
            "password": "your_password",
            "description": "Server added via Python script",
            "is_active": True,
            "tags": ["automated", "python"]
        }
        
        result = await manager.add_server(server_data)
        print(f"✅ Server added: {result}")
        
        # Test connection
        if "id" in result:
            test_result = await manager.test_connection(result["id"])
            print(f"🔗 Connection test: {test_result}")

if __name__ == "__main__":
    asyncio.run(main())
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   ```
   {"detail": "Could not validate credentials"}
   ```
   - Solution: Check your username/password or get a new token

2. **Server Name Already Exists**
   ```
   {"detail": "Server name already exists"}
   ```
   - Solution: Use a unique server name

3. **Missing Authentication Method**
   ```
   {"detail": "Either password or private_key must be provided"}
   ```
   - Solution: Provide either `password` or `private_key`

4. **Connection Test Failed**
   ```
   {"success": false, "message": "SSH connection failed"}
   ```
   - Solution: Check server credentials, network connectivity, and SSH service

### Validation Errors

The API validates input data:
- Server name cannot be empty
- Host cannot be empty
- Port must be between 1 and 65535
- At least one authentication method required

## Security Best Practices

1. **Use SSH Keys**: Prefer SSH key authentication over passwords
2. **Strong Passwords**: Use strong passwords if using password authentication
3. **Limit Access**: Set `is_active: false` for servers not in use
4. **Regular Updates**: Keep server credentials updated
5. **Monitor Logs**: Check application logs for connection issues

## Next Steps

After adding servers:
1. **Test connections** to ensure they work
2. **Check Xray service status** on each server
3. **Review client configurations** 
4. **Set up automated expiry checking**
5. **Monitor server health** regularly

For more information, see the main README.md and SETUP.md files. 