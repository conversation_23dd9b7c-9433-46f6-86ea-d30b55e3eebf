#!/bin/bash

# Test script for Docker logging permissions
# This script builds and tests the Docker container to ensure logging works properly

set -e

echo "🐳 Testing Docker Logging Permissions"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to cleanup containers and volumes
cleanup() {
    print_status $YELLOW "🧹 Cleaning up test containers and volumes..."
    
    # Stop and remove test container
    docker stop vpsscripthelper-test 2>/dev/null || true
    docker rm vpsscripthelper-test 2>/dev/null || true
    
    # Remove test volumes
    docker volume rm vpsscripthelper-test-logs 2>/dev/null || true
    docker volume rm vpsscripthelper-test-data 2>/dev/null || true
    
    print_status $GREEN "✅ Cleanup completed"
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Step 1: Build the Docker image
print_status $BLUE "🔨 Building Docker image..."
docker build -t vpsscripthelper-blueblue:test .

if [ $? -eq 0 ]; then
    print_status $GREEN "✅ Docker image built successfully"
else
    print_status $RED "❌ Docker image build failed"
    exit 1
fi

# Step 2: Test with named volumes (production-like)
print_status $BLUE "🧪 Testing with named volumes (production-like)..."

docker run -d \
    --name vpsscripthelper-test \
    -p 8001:8000 \
    -v vpsscripthelper-test-logs:/app/logs \
    -v vpsscripthelper-test-data:/app/data \
    -e ENVIRONMENT=development \
    -e DEBUG=true \
    -e LOG_LEVEL=INFO \
    vpsscripthelper-blueblue:test

# Wait for container to start
print_status $YELLOW "⏳ Waiting for container to start..."
sleep 10

# Check if container is running
if docker ps | grep -q vpsscripthelper-test; then
    print_status $GREEN "✅ Container is running"
else
    print_status $RED "❌ Container failed to start"
    print_status $YELLOW "📋 Container logs:"
    docker logs vpsscripthelper-test
    exit 1
fi

# Check container logs for permission issues
print_status $BLUE "📋 Checking container logs for permission issues..."
docker logs vpsscripthelper-test 2>&1 | grep -i "permission\|error\|warning" || true

# Test if logs directory is writable
print_status $BLUE "🔍 Testing log file creation..."
docker exec vpsscripthelper-test ls -la /app/logs/
docker exec vpsscripthelper-test touch /app/logs/test-write.log

if [ $? -eq 0 ]; then
    print_status $GREEN "✅ Log directory is writable"
    docker exec vpsscripthelper-test rm -f /app/logs/test-write.log
else
    print_status $RED "❌ Log directory is not writable"
fi

# Test API endpoint
print_status $BLUE "🌐 Testing API endpoint..."
sleep 5  # Give the app more time to start

response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8001/api/v1/health || echo "000")

if [ "$response" = "200" ]; then
    print_status $GREEN "✅ API endpoint is responding"
else
    print_status $RED "❌ API endpoint is not responding (HTTP $response)"
    print_status $YELLOW "📋 Recent container logs:"
    docker logs --tail 20 vpsscripthelper-test
fi

# Show final container status
print_status $BLUE "📊 Final container status:"
docker exec vpsscripthelper-test ps aux
docker exec vpsscripthelper-test ls -la /app/logs/

print_status $GREEN "🎉 Docker logging test completed!"
print_status $YELLOW "💡 To run the container manually:"
print_status $YELLOW "   docker run -p 8000:8000 -v logs:/app/logs vpsscripthelper-blueblue:test"
