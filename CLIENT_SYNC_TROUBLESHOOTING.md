# Client Sync Troubleshooting Guide

This guide helps you troubleshoot and fix client synchronization issues in VPSScriptHelper-BlueBlue.

## Overview

The client sync system imports clients from your server's Xray configuration (`/etc/xray/config.json`) into the local database. Sometimes there are invalid or incomplete client entries that cause sync errors.

## Common Issues and Solutions

### 1. Invalid Client Data in Server Configuration

**Problem**: Clients with missing required fields (email, expired_date, or client_id)

**Symptoms**:
```json
{
  "success": true,
  "imported_count": 404,
  "errors": [
    "Error syncing client unknown: Validation failed for Client ID: abea41aa-e186-4428-9562-2da557130fc3, Email: N/A - Email is missing; Expired date is missing",
    "Error syncing client admin666: Validation failed for Client ID: 144104ca-7883-4b9c-873c-5062e38ede18, Email: admin666 - Expired date is missing"
  ]
}
```

**Solution**: Use the analysis endpoint to identify and fix invalid clients.

## Diagnostic Tools

### 1. Analyze Invalid Clients

```bash
# Analyze invalid clients in server configuration
curl -X GET "http://localhost:8000/api/v1/clients/sync/analyze/2" \
     -H "Authorization: Bearer $TOKEN"
```

**Example Response**:
```json
{
  "success": true,
  "server_id": 2,
  "server_name": "Shinjiru Server1",
  "total_clients": 406,
  "valid_clients": 404,
  "invalid_clients": 2,
  "invalid_client_details": [
    {
      "index": 404,
      "client_id": "abea41aa-e186-4428-9562-2da557130fc3",
      "email": "",
      "shopee_username": "",
      "expired_date": "",
      "issues": ["missing_email", "missing_expired_date"],
      "raw_data": {"id": "abea41aa-e186-4428-9562-2da557130fc3"}
    },
    {
      "index": 405,
      "client_id": "144104ca-7883-4b9c-873c-5062e38ede18",
      "email": "admin666",
      "shopee_username": "",
      "expired_date": "",
      "issues": ["missing_expired_date"],
      "raw_data": {"id": "144104ca-7883-4b9c-873c-5062e38ede18", "email": "admin666"}
    }
  ],
  "analysis": {
    "missing_client_id": 0,
    "missing_email": 1,
    "missing_expired_date": 2,
    "invalid_date_format": 0
  }
}
```

### 2. Compare Sync Status

```bash
# Compare clients between server config and database
curl -X GET "http://localhost:8000/api/v1/clients/sync/compare/2" \
     -H "Authorization: Bearer $TOKEN"
```

### 3. Enhanced Sync with Better Error Reporting

```bash
# Sync with detailed error messages
curl -X POST "http://localhost:8000/api/v1/clients/sync/server/2" \
     -H "Authorization: Bearer $TOKEN"
```

## Fixing Invalid Clients

### Option 1: Manual Fix via SSH

1. **Connect to your server**:
   ```bash
   ssh root@your-server-ip
   ```

2. **Backup the configuration**:
   ```bash
   cp /etc/xray/config.json /etc/xray/config.json.backup
   ```

3. **Edit the configuration**:
   ```bash
   nano /etc/xray/config.json
   ```

4. **Fix the invalid clients**:
   
   **For client with missing email and expired_date**:
   ```json
   // BEFORE (invalid):
   {
     "id": "abea41aa-e186-4428-9562-2da557130fc3"
   }
   
   // AFTER (fixed):
   {
     "id": "abea41aa-e186-4428-9562-2da557130fc3",
     "shopeeUsername": "admin-client",
     "expired_date": "31-12-2025",
     "email": "<EMAIL>"
   }
   ```
   
   **For client with missing expired_date**:
   ```json
   // BEFORE (invalid):
   {
     "id": "144104ca-7883-4b9c-873c-5062e38ede18",
     "email": "admin666"
   }
   
   // AFTER (fixed):
   {
     "id": "144104ca-7883-4b9c-873c-5062e38ede18",
     "shopeeUsername": "admin666",
     "expired_date": "31-12-2025",
     "email": "admin666"
   }
   ```

5. **Restart Xray service**:
   ```bash
   systemctl restart xray
   ```

6. **Test the configuration**:
   ```bash
   systemctl status xray
   ```

### Option 2: Remove Invalid Clients

If the invalid clients are not needed, you can remove them:

1. **Edit the configuration**:
   ```bash
   nano /etc/xray/config.json
   ```

2. **Remove the invalid client entries** completely from the clients array.

3. **Restart Xray service**:
   ```bash
   systemctl restart xray
   ```

### Option 3: Automated Fix Script

Create a script to automatically fix common issues:

```python
#!/usr/bin/env python3
import json
import sys
from datetime import datetime, timedelta

def fix_invalid_clients(config_file):
    """Fix invalid clients in Xray configuration"""
    
    # Read configuration
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # Find clients array
    clients = None
    for inbound in config.get('inbounds', []):
        if 'clients' in inbound.get('settings', {}):
            clients = inbound['settings']['clients']
            break
    
    if not clients:
        print("No clients found in configuration")
        return
    
    # Fix invalid clients
    fixed_count = 0
    default_expiry = (datetime.now() + timedelta(days=365)).strftime("%d-%m-%Y")
    
    for i, client in enumerate(clients):
        fixed = False
        
        # Fix missing email
        if not client.get('email'):
            client['email'] = f"client-{client.get('id', i)}@example.com"
            fixed = True
        
        # Fix missing shopeeUsername
        if not client.get('shopeeUsername'):
            client['shopeeUsername'] = client.get('email', f"client-{i}")
            fixed = True
        
        # Fix missing expired_date
        if not client.get('expired_date'):
            client['expired_date'] = default_expiry
            fixed = True
        
        if fixed:
            fixed_count += 1
            print(f"Fixed client {i}: {client.get('email')}")
    
    # Write back configuration
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Fixed {fixed_count} clients")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python fix_clients.py /etc/xray/config.json")
        sys.exit(1)
    
    fix_invalid_clients(sys.argv[1])
```

## Verification Steps

After fixing invalid clients:

### 1. Re-analyze the Configuration

```bash
curl -X GET "http://localhost:8000/api/v1/clients/sync/analyze/2" \
     -H "Authorization: Bearer $TOKEN"
```

Expected result: `"invalid_clients": 0`

### 2. Re-sync Clients

```bash
curl -X POST "http://localhost:8000/api/v1/clients/sync/server/2" \
     -H "Authorization: Bearer $TOKEN"
```

Expected result: No errors, all clients imported/updated

### 3. Verify Sync Status

```bash
curl -X GET "http://localhost:8000/api/v1/clients/sync/compare/2" \
     -H "Authorization: Bearer $TOKEN"
```

Expected result: `"is_synchronized": true`

## Prevention

### 1. Validate Client Data Before Adding

When adding clients manually to Xray config, ensure they have:
- `id`: Valid UUID
- `email`: Valid email address
- `shopeeUsername`: Username (can be same as email)
- `expired_date`: Date in DD-MM-YYYY format

### 2. Use the API for Client Management

Instead of manually editing Xray config, use the VPSScriptHelper API:

```bash
# Add client via API (automatically validates and syncs)
curl -X POST "http://localhost:8000/api/v1/clients/" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "server_id": 2,
       "email": "<EMAIL>",
       "shopee_username": "newclient",
       "expired_date": "31-12-2025"
     }'
```

### 3. Regular Sync Monitoring

Set up automated monitoring to check sync status:

```python
#!/usr/bin/env python3
import httpx
import asyncio

async def monitor_sync_status():
    """Monitor sync status for all servers"""
    async with httpx.AsyncClient() as client:
        # Get all servers
        response = await client.get('http://localhost:8000/api/v1/servers/')
        servers = response.json()
        
        for server in servers:
            server_id = server['id']
            
            # Check sync status
            response = await client.get(f'http://localhost:8000/api/v1/clients/sync/compare/{server_id}')
            if response.status_code == 200:
                comparison = response.json()
                if not comparison['is_synchronized']:
                    print(f"⚠️ Server {server['name']} is not synchronized!")
                    print(f"   Config: {comparison['total_in_config']}, DB: {comparison['total_in_database']}")
                else:
                    print(f"✅ Server {server['name']} is synchronized")

if __name__ == "__main__":
    asyncio.run(monitor_sync_status())
```

## API Endpoints Summary

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/clients/sync/analyze/{server_id}` | GET | Analyze invalid clients |
| `/api/v1/clients/sync/server/{server_id}` | POST | Sync specific server |
| `/api/v1/clients/sync/all` | POST | Sync all servers |
| `/api/v1/clients/sync/compare/{server_id}` | GET | Compare sync status |
| `/api/v1/clients/sync/cleanup/{server_id}` | POST | Remove orphaned clients |

## Troubleshooting Checklist

- [ ] Run analysis to identify invalid clients
- [ ] Check server SSH connectivity
- [ ] Verify Xray configuration file exists and is readable
- [ ] Backup configuration before making changes
- [ ] Fix invalid client data (add missing fields)
- [ ] Restart Xray service after config changes
- [ ] Re-run sync and verify no errors
- [ ] Check sync comparison shows synchronized status
- [ ] Monitor logs for any ongoing issues

## Getting Help

If you continue to have sync issues:

1. **Check application logs**:
   ```bash
   tail -f logs/app.log
   ```

2. **Test server connectivity**:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/servers/2/test-connection" \
        -H "Authorization: Bearer $TOKEN"
   ```

3. **Verify Xray service status**:
   ```bash
   curl -X GET "http://localhost:8000/api/v1/servers/2/service-status" \
        -H "Authorization: Bearer $TOKEN"
   ```

4. **Check server configuration**:
   ```bash
   curl -X GET "http://localhost:8000/api/v1/servers/2/config" \
        -H "Authorization: Bearer $TOKEN"
   ``` 