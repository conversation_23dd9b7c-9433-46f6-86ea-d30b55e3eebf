# How to Manage Clients in VPSScriptHelper-BlueBlue

This guide explains how to manage VPN clients in your VPSScriptHelper-BlueBlue system, including creating, updating, and monitoring clients across your servers.

## Prerequisites

1. **Application Running**: Ensure the application is running
2. **Servers Added**: You need to have servers configured first
3. **Authentication**: You need to be logged in (or use development mode)

## Client Structure

Based on your Xray configuration, clients have this structure:
```json
{
  "id": "05807d1c-1646-4e0e-939f-195a560793ac",
  "shopeeUsername": "240715NXGCYW98-MG",
  "expired_date": "01-03-2025",
  "email": "240715NXGCYW98-MG"
}
```

**Note**: The `email` field can contain any custom identifier (like "jakaentah-18368" or "240715NXGCYW98-MG") and doesn't need to be a valid email format. This provides flexibility for your naming conventions.

## Method 1: Using API Documentation (Recommended)

### Access API Documentation
Open: `http://localhost:8000/docs`

### Authenticate
1. Click **"Authorize"**
2. Login with your credentials

## Method 2: Using curl Commands

### Get Authentication Token
```bash
TOKEN=$(curl -s -X POST "http://localhost:8000/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "admin123"}' \
     | jq -r '.access_token')
```

## Client Management Operations

### 1. List All Clients

```bash
# Get all clients with pagination
curl -X GET "http://localhost:8000/api/v1/clients/?skip=0&limit=100" \
     -H "Authorization: Bearer $TOKEN"

# Filter by server
curl -X GET "http://localhost:8000/api/v1/clients/?server_id=1" \
     -H "Authorization: Bearer $TOKEN"

# Filter by active status
curl -X GET "http://localhost:8000/api/v1/clients/?is_active=true" \
     -H "Authorization: Bearer $TOKEN"

# Filter by expiry status
curl -X GET "http://localhost:8000/api/v1/clients/?is_expired=false" \
     -H "Authorization: Bearer $TOKEN"

# Search clients
curl -X GET "http://localhost:8000/api/v1/clients/?search=240715" \
     -H "Authorization: Bearer $TOKEN"
```

### 2. Create a Single Client

```bash
# Example with custom identifier (not email format)
curl -X POST "http://localhost:8000/api/v1/clients/" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "server_id": 1,
       "email": "jakaentah-18368",
       "shopee_username": "240715NXGCYW98-MG",
       "expired_date": "01-03-2025",
       "description": "VIP client",
       "notes": "Premium subscription"
     }'

# Example with actual email format
curl -X POST "http://localhost:8000/api/v1/clients/" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "server_id": 1,
       "email": "<EMAIL>",
       "shopee_username": "USER-EXAMPLE",
       "expired_date": "01-03-2025",
       "description": "Standard client",
       "notes": "Regular subscription"
     }'
```

### 3. Create Client with Custom UUID

```bash
curl -X POST "http://localhost:8000/api/v1/clients/" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "server_id": 1,
       "client_id": "05807d1c-1646-4e0e-939f-195a560793ac",
       "email": "<EMAIL>",
       "shopee_username": "CUSTOM-USER",
       "expired_date": "15-04-2025",
       "description": "Client with custom UUID"
     }'
```

### 4. Bulk Create Clients

```bash
curl -X POST "http://localhost:8000/api/v1/clients/bulk" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "server_id": 1,
       "clients": [
         {
           "email": "<EMAIL>",
           "shopee_username": "CLIENT1-USER",
           "expired_date": "01-04-2025",
           "description": "Bulk client 1"
         },
         {
           "email": "<EMAIL>",
           "shopee_username": "CLIENT2-USER", 
           "expired_date": "15-04-2025",
           "description": "Bulk client 2"
         },
         {
           "email": "<EMAIL>",
           "shopee_username": "CLIENT3-USER",
           "expired_date": "30-04-2025",
           "description": "Bulk client 3"
         }
       ]
     }'
```

### 5. Get Specific Client

```bash
curl -X GET "http://localhost:8000/api/v1/clients/1" \
     -H "Authorization: Bearer $TOKEN"
```

### 6. Update Client

```bash
curl -X PUT "http://localhost:8000/api/v1/clients/1" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "expired_date": "01-06-2025",
       "description": "Updated description",
       "notes": "Extended subscription"
     }'
```

### 7. Delete Client

```bash
curl -X DELETE "http://localhost:8000/api/v1/clients/1" \
     -H "Authorization: Bearer $TOKEN"
```

### 8. Extend Client Expiry

```bash
# Extend by 30 days
curl -X POST "http://localhost:8000/api/v1/clients/1/extend?days=30" \
     -H "Authorization: Bearer $TOKEN"

# Extend by 90 days
curl -X POST "http://localhost:8000/api/v1/clients/1/extend?days=90" \
     -H "Authorization: Bearer $TOKEN"
```

### 9. Get Client Expiry Information

```bash
# Get expiry info for all clients on a server
curl -X GET "http://localhost:8000/api/v1/clients/server/1/expiry" \
     -H "Authorization: Bearer $TOKEN"
```

### 10. Sync Clients from Server Configuration

```bash
# Sync clients from a specific server's Xray config to database
curl -X POST "http://localhost:8000/api/v1/clients/sync/server/1" \
     -H "Authorization: Bearer $TOKEN"

# Sync clients from all active servers
curl -X POST "http://localhost:8000/api/v1/clients/sync/all" \
     -H "Authorization: Bearer $TOKEN"

# Compare clients between server config and database
curl -X GET "http://localhost:8000/api/v1/clients/sync/compare/1" \
     -H "Authorization: Bearer $TOKEN"

# Clean up orphaned clients (remove from DB if not in server config)
curl -X POST "http://localhost:8000/api/v1/clients/sync/cleanup/1" \
     -H "Authorization: Bearer $TOKEN"
```

## Client Fields Explained

### Required Fields
- **server_id**: ID of the server where client will be added
- **email**: Client email or custom identifier (can be any string, not required to be valid email format)
- **expired_date**: Expiry date in DD-MM-YYYY format or "lifetime" for infinite expiration

### Optional Fields
- **client_id**: UUID (auto-generated if not provided)
- **shopee_username**: Shopee username (defaults to email if not provided)
- **description**: Client description
- **notes**: Additional notes
- **is_active**: Whether client is active (default: true)

### Expiration Date Formats
- **DD-MM-YYYY**: Standard date format (e.g., "15-06-2025")
- **"lifetime"**: For clients with infinite expiration (never expires)

Examples:
```bash
# Client with standard expiry date
curl -X POST "http://localhost:8000/api/v1/clients/" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "server_id": 1,
       "email": "<EMAIL>",
       "expired_date": "15-06-2025",
       "description": "Standard client with expiry"
     }'

# Client with lifetime access (never expires)
curl -X POST "http://localhost:8000/api/v1/clients/" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "server_id": 1,
       "email": "<EMAIL>",
       "expired_date": "lifetime",
       "description": "VIP client with lifetime access"
     }'
```

### Response Fields
- **id**: Database ID
- **client_id**: UUID used in Xray config
- **is_active**: Whether client is active
- **is_expired**: Whether client has expired
- **days_until_expiry**: Days until expiry (negative if expired)
- **data_usage**: Data usage in bytes
- **last_connected**: Last connection timestamp
- **created_at/updated_at**: Timestamps

## Advanced Client Management

### Python Script for Client Management

```python
#!/usr/bin/env python3
import httpx
import asyncio
import json
from datetime import datetime, timedelta

class ClientManager:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.token = None

    async def login(self, username, password):
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/auth/login",
                json={"username": username, "password": password}
            )
            if response.status_code == 200:
                self.token = response.json()["access_token"]
                return True
            return False

    async def create_client(self, server_id, email, shopee_username, expired_date, description=None):
        headers = {"Authorization": f"Bearer {self.token}"}
        client_data = {
            "server_id": server_id,
            "email": email,
            "shopee_username": shopee_username,
            "expired_date": expired_date,
            "description": description
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/clients/",
                headers=headers,
                json=client_data
            )
            return response.json()

    async def list_clients(self, server_id=None, is_expired=None, search=None):
        headers = {"Authorization": f"Bearer {self.token}"}
        params = {}
        if server_id:
            params["server_id"] = server_id
        if is_expired is not None:
            params["is_expired"] = is_expired
        if search:
            params["search"] = search
            
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/clients/",
                headers=headers,
                params=params
            )
            return response.json()

    async def extend_client(self, client_id, days):
        headers = {"Authorization": f"Bearer {self.token}"}
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/clients/{client_id}/extend",
                headers=headers,
                params={"days": days}
            )
            return response.json()

         async def get_expiry_info(self, server_id):
         headers = {"Authorization": f"Bearer {self.token}"}
         async with httpx.AsyncClient() as client:
             response = await client.get(
                 f"{self.base_url}/api/v1/clients/server/{server_id}/expiry",
                 headers=headers
             )
             return response.json()

     async def sync_server_clients(self, server_id):
         headers = {"Authorization": f"Bearer {self.token}"}
         async with httpx.AsyncClient() as client:
             response = await client.post(
                 f"{self.base_url}/api/v1/clients/sync/server/{server_id}",
                 headers=headers
             )
             return response.json()

     async def compare_clients(self, server_id):
         headers = {"Authorization": f"Bearer {self.token}"}
         async with httpx.AsyncClient() as client:
             response = await client.get(
                 f"{self.base_url}/api/v1/clients/sync/compare/{server_id}",
                 headers=headers
             )
             return response.json()

async def main():
    manager = ClientManager()
    
    # Login
    if await manager.login("admin", "admin123"):
        print("✅ Login successful")
        
        # Create a client
        future_date = (datetime.now() + timedelta(days=30)).strftime("%d-%m-%Y")
        result = await manager.create_client(
            server_id=1,
            email="<EMAIL>",
            shopee_username="TEST-CLIENT",
            expired_date=future_date,
            description="Test client created via Python"
        )
        print(f"✅ Client created: {result}")
        
        # List clients
        clients = await manager.list_clients(server_id=1)
        print(f"📋 Found {clients['total']} clients")
        
                 # Get expiry information
         expiry_info = await manager.get_expiry_info(1)
         print(f"⏰ Expiry info: {len(expiry_info)} clients checked")
         
         # Sync clients from server configuration
         sync_result = await manager.sync_server_clients(1)
         print(f"🔄 Sync result: {sync_result}")
         
         # Compare clients
         comparison = await manager.compare_clients(1)
         print(f"📊 Comparison: {comparison['total_in_config']} in config, {comparison['total_in_database']} in DB")

if __name__ == "__main__":
    asyncio.run(main())
```

### Batch Operations Script

```python
#!/usr/bin/env python3
import httpx
import asyncio
import csv
from datetime import datetime, timedelta

async def bulk_create_from_csv(csv_file, server_id, token):
    """Create clients from CSV file"""
    clients = []
    
    with open(csv_file, 'r') as file:
        reader = csv.DictReader(file)
        for row in reader:
            clients.append({
                "email": row["email"],
                "shopee_username": row.get("shopee_username", row["email"]),
                "expired_date": row["expired_date"],
                "description": row.get("description", "")
            })
    
    bulk_data = {
        "server_id": server_id,
        "clients": clients
    }
    
    headers = {"Authorization": f"Bearer {token}"}
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/v1/clients/bulk",
            headers=headers,
            json=bulk_data
        )
        return response.json()

# Example CSV format:
# email,shopee_username,expired_date,description
# <EMAIL>,CLIENT1,01-04-2025,VIP Client
# <EMAIL>,CLIENT2,15-04-2025,Premium Client
```

## Monitoring and Maintenance

### 1. Check Expired Clients

```bash
# Get all expired clients
curl -X GET "http://localhost:8000/api/v1/clients/?is_expired=true" \
     -H "Authorization: Bearer $TOKEN"

# Get clients expiring soon
curl -X GET "http://localhost:8000/api/v1/clients/server/1/expiry" \
     -H "Authorization: Bearer $TOKEN"
```

### 2. Extend Multiple Clients

```bash
# Script to extend all clients expiring in next 7 days
#!/bin/bash

# Get clients expiring soon
EXPIRING_CLIENTS=$(curl -s -X GET "http://localhost:8000/api/v1/clients/server/1/expiry" \
     -H "Authorization: Bearer $TOKEN" | jq -r '.[] | select(.days_until_expiry <= 7 and .days_until_expiry > 0) | .client_id')

# Extend each client by 30 days
for client_id in $EXPIRING_CLIENTS; do
    echo "Extending client: $client_id"
    curl -X POST "http://localhost:8000/api/v1/clients/$client_id/extend?days=30" \
         -H "Authorization: Bearer $TOKEN"
done
```

### 3. Generate Client Reports

```python
#!/usr/bin/env python3
import httpx
import asyncio
import json
from datetime import datetime

async def generate_client_report(server_id, token):
    """Generate comprehensive client report"""
    headers = {"Authorization": f"Bearer {token}"}
    
    async with httpx.AsyncClient() as client:
        # Get all clients
        response = await client.get(
            f"http://localhost:8000/api/v1/clients/?server_id={server_id}&limit=1000",
            headers=headers
        )
        clients_data = response.json()
        
        # Get expiry information
        response = await client.get(
            f"http://localhost:8000/api/v1/clients/server/{server_id}/expiry",
            headers=headers
        )
        expiry_data = response.json()
    
    # Generate report
    total_clients = clients_data["total"]
    active_clients = len([c for c in clients_data["clients"] if c["is_active"]])
    expired_clients = len([e for e in expiry_data if e["is_expired"]])
    expiring_soon = len([e for e in expiry_data if e["days_until_expiry"] <= 7 and not e["is_expired"]])
    
    report = {
        "server_id": server_id,
        "generated_at": datetime.now().isoformat(),
        "summary": {
            "total_clients": total_clients,
            "active_clients": active_clients,
            "expired_clients": expired_clients,
            "expiring_soon": expiring_soon
        },
        "clients": clients_data["clients"],
        "expiry_details": expiry_data
    }
    
    # Save report
    with open(f"client_report_server_{server_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print(f"📊 Report generated for server {server_id}")
    print(f"   Total: {total_clients}, Active: {active_clients}")
    print(f"   Expired: {expired_clients}, Expiring Soon: {expiring_soon}")
    
    return report
```

## Client Synchronization

### Import Existing Clients from Servers

If you have existing clients in your server configurations, you can import them:

```bash
# Import clients from a specific server
curl -X POST "http://localhost:8000/api/v1/clients/sync/server/1" \
     -H "Authorization: Bearer $TOKEN"

# Expected response:
{
  "success": true,
  "server_id": 1,
  "server_name": "Production Server",
  "total_remote_clients": 15,
  "imported_count": 12,
  "updated_count": 2,
  "skipped_count": 1,
  "errors": [],
  "message": "Sync completed: 14 clients processed"
}
```

### Sync All Servers at Once

```bash
# Import clients from all active servers
curl -X POST "http://localhost:8000/api/v1/clients/sync/all" \
     -H "Authorization: Bearer $TOKEN"
```

### Compare Database vs Server Configuration

```bash
# Check sync status
curl -X GET "http://localhost:8000/api/v1/clients/sync/compare/1" \
     -H "Authorization: Bearer $TOKEN"

# Response shows differences:
{
  "success": true,
  "server_id": 1,
  "server_name": "Production Server",
  "total_in_config": 15,
  "total_in_database": 12,
  "in_sync": 10,
  "missing_in_database": 5,
  "missing_in_config": 2,
  "is_synchronized": false,
  "missing_in_db_details": [...],
  "missing_in_config_details": [...]
}
```

### Clean Up Orphaned Clients

Remove clients from database that no longer exist in server config:

```bash
curl -X POST "http://localhost:8000/api/v1/clients/sync/cleanup/1" \
     -H "Authorization: Bearer $TOKEN"
```

### Automated Sync Script

```python
#!/usr/bin/env python3
import httpx
import asyncio

async def auto_sync_all_servers():
    """Automatically sync all servers and report results"""
    
    # Login
    async with httpx.AsyncClient() as client:
        login_response = await client.post(
            "http://localhost:8000/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Sync all servers
        sync_response = await client.post(
            "http://localhost:8000/api/v1/clients/sync/all",
            headers=headers
        )
        sync_result = sync_response.json()
        
        print(f"🔄 Sync completed:")
        print(f"   Servers processed: {sync_result['total_servers']}")
        print(f"   Clients imported: {sync_result['total_imported']}")
        print(f"   Clients updated: {sync_result['total_updated']}")
        print(f"   Errors: {sync_result['total_errors']}")
        
        # Show details for each server
        for server_result in sync_result['server_results']:
            if server_result['success']:
                print(f"   📡 {server_result['server_name']}: "
                      f"{server_result['imported_count']} imported, "
                      f"{server_result['updated_count']} updated")
            else:
                print(f"   ❌ {server_result['server_name']}: {server_result['error']}")

if __name__ == "__main__":
    asyncio.run(auto_sync_all_servers())
```

## Integration with Server Configuration

The client management system automatically:

1. **Syncs with Xray Config**: Creates/updates/deletes clients in `/etc/xray/config.json`
2. **Maintains Database**: Keeps local database in sync with server configs
3. **Handles Expiry**: Automatically detects and can remove expired clients
4. **Validates Data**: Ensures proper UUID format and date validation
5. **Imports Existing**: Can import clients from existing server configurations
6. **Bidirectional Sync**: Keeps database and server configs synchronized

## Troubleshooting

### Common Issues

1. **Client Creation Failed**
   ```
   {"detail": "Client with this email already exists on this server"}
   ```
   - Solution: Use unique emails per server or update existing client

2. **Invalid Date Format**
   ```
   {"detail": "Date must be in DD-MM-YYYY format"}
   ```
   - Solution: Use correct format like "01-03-2025"

3. **Server Configuration Update Failed**
   ```
   {"detail": "Failed to add client to server configuration"}
   ```
   - Solution: Check server SSH connection and Xray config file permissions

4. **UUID Validation Error**
   ```
   {"detail": "client_id must be a valid UUID"}
   ```
   - Solution: Use proper UUID format or let system generate one

### Best Practices

1. **Use Bulk Operations**: For creating multiple clients, use bulk endpoint
2. **Monitor Expiry**: Regularly check expiry status and extend as needed
3. **Backup Configs**: Server configs are automatically backed up before changes
4. **Validate Dates**: Always use DD-MM-YYYY format for dates
5. **Unique Emails**: Use unique email addresses per server
6. **Regular Cleanup**: Remove expired clients to keep configs clean

## Next Steps

After setting up client management:
1. **Set up automated expiry monitoring**
2. **Create client renewal workflows**
3. **Implement usage tracking**
4. **Set up client notifications**
5. **Create backup and restore procedures**

For more information, see the main README.md and server management guides. 